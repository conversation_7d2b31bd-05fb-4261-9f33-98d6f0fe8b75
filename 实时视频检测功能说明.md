# 实时视频检测功能说明

## 功能概述

在 `detected.vue` 中新增了对实时视频流的检测识别功能，支持从 RTSP 流中每1秒抽取一帧进行目标检测，并将检测结果显示在检测结果区域。

## 主要功能

### 1. 实时视频流连接
- 支持 RTSP 流连接
- 使用 WebRTC 技术进行视频流传输
- 连接状态实时监控

### 2. 实时检测控制
- 启动/停止实时检测按钮
- 每1秒自动抽取一帧进行检测
- 支持检测模型选择（YOLO、小样本等）

### 3. 帧数据处理
- 使用 Canvas 从视频流中提取帧图像
- 将帧数据转换为 base64 格式
- 发送到后端进行检测处理

### 4. 检测结果显示
- 检测结果显示在统一的检测结果区域
- 支持累计结果和单次结果显示模式
- 实时统计检测数量和状态

## 使用方法

### 1. 连接 RTSP 流
1. 选择"实时流"输入类型
2. 点击"配置RTSP流"按钮
3. 输入 RTSP 地址（格式：`ip:port/path`）
4. 点击"连接"建立视频流连接

### 2. 启动实时检测
1. 确保 RTSP 流已连接
2. 选择检测模型（如需要）
3. 点击"开始实时检测"按钮
4. 系统将每1秒自动检测一次

### 3. 查看检测结果
- 检测结果会自动显示在右侧检测结果区域
- 可以点击结果查看详细信息
- 支持保存、修正等操作

## 技术实现

### 前端实现
- **帧提取**：使用 Canvas API 从 video 元素中提取当前帧
- **定时器**：setInterval 每1000ms 触发一次检测
- **数据传输**：将帧数据转换为 base64 格式发送到后端
- **状态管理**：完整的检测状态控制和错误处理

### 后端接口
- **接口路径**：`/api/detected/detect-frame`
- **请求参数**：
  - `frame_data`: base64 编码的图像数据
  - `timestamp`: 时间戳
  - `image_type`: 图像类型（visible/infrared）
- **返回结果**：检测结果、边界框、统计信息等

### 错误处理
- 连接状态监控
- 跨域问题处理
- 网络超时处理
- 连续错误自动停止机制

## 性能优化

### 1. 内存管理
- 限制检测结果列表长度（最多200条）
- 自动清理过期的检测结果
- Canvas 尺寸动态调整

### 2. 网络优化
- 10秒请求超时限制
- 5MB 数据大小限制
- 错误重试机制

### 3. 用户体验
- 实时状态指示器
- 检测进度显示
- 错误提示和自动恢复

## 配置说明

### 检测间隔
默认每1秒检测一次，可在代码中修改 `frameInterval` 参数：
```javascript
realtimeDetection: {
  frameInterval: 1000, // 毫秒，1000 = 1秒
}
```

### Canvas 尺寸
默认最大640像素宽度，自动计算高度保持宽高比：
```javascript
canvas.width = Math.min(640, video.videoWidth)
canvas.height = canvas.width / aspectRatio
```

## 注意事项

1. **RTSP 流要求**：确保 RTSP 流地址正确且可访问
2. **浏览器兼容性**：需要支持 Canvas 和 WebRTC 的现代浏览器
3. **网络稳定性**：实时检测对网络稳定性有一定要求
4. **性能考虑**：长时间运行可能消耗较多系统资源

## 故障排除

### 常见问题
1. **连接失败**：检查 RTSP 地址和网络连接
2. **检测不工作**：确认视频流正在播放且检测已启动
3. **跨域错误**：可能需要配置服务器 CORS 设置
4. **性能问题**：可以调整检测间隔或降低视频质量

### 调试信息
系统会在浏览器控制台输出详细的调试信息，包括：
- 帧捕获状态
- 检测请求和响应
- 错误信息和处理结果
