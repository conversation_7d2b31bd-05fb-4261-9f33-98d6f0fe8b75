from fastapi import APIRouter, HTTPException, Query, Path, UploadFile, File, Form
from pydantic import BaseModel, Field
from typing import List, Dict, Optional
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.db import db_manager
from utils.tools import (format_response, is_image_file, is_valid_upload_file, read_json_file,
                        write_json_file, delete_file_safely, read_text_file,
                        save_uploaded_file, get_annotation_file_path, rename_file_safely,
                        create_category_tree_with_counts)

# 创建实验路由器（匹配前端接口路径）
router = APIRouter(prefix="/api/exp", tags=["实验样本管理"])

# 请求模型
class SampleCreate(BaseModel):
    name: str = Field(..., description="样本名称")
    category_id: Optional[int] = Field(None, description="分类ID")
    file_path: Optional[str] = Field(None, description="文件路径")
    description: Optional[str] = Field(None, description="描述信息")
    metadata: Optional[Dict] = Field(None, description="元数据")

class SampleUpdate(BaseModel):
    name: Optional[str] = Field(None, description="样本名称")
    category_id: Optional[int] = Field(None, description="分类ID")
    file_path: Optional[str] = Field(None, description="文件路径")
    description: Optional[str] = Field(None, description="描述信息")
    metadata: Optional[Dict] = Field(None, description="元数据")

class SampleResponse(BaseModel):
    id: int
    name: str
    category_id: Optional[int]
    file_path: Optional[str]
    description: Optional[str]
    metadata: Optional[Dict]
    created_at: str
    updated_at: str

# 新增请求模型
class CategoryTreeSave(BaseModel):
    tree_data: List[Dict] = Field(..., description="分类树数据")

class SampleRename(BaseModel):
    id: int = Field(..., description="样本ID")
    new_name: str = Field(..., description="新名称")

class BatchDelete(BaseModel):
    ids: List[int] = Field(..., description="要删除的样本ID列表")

# ==================== 新增的前端匹配接口 ====================

@router.get("/getCategoryTree", summary="获取类别树")
async def get_category_tree():
    """
    取得类别树，读取后台类别树json文件，并获取对应类别的样本数量
    """
    try:
        # 读取配置文件
        tree_data = read_json_file(r"./conf/treeList.json")
        if not tree_data:
            raise HTTPException(status_code=500, detail="无法读取分类配置文件")

        return format_response(
            data=tree_data,
            message="获取类别树成功"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取类别树失败: {str(e)}")

@router.post("/saveCategoryTree", summary="保存类别树")
async def save_category_tree(data: CategoryTreeSave):
    """
    保存类别树，将确定的类别树传到后端保存到json文件中
    """
    try:
        # 保存到JSON文件
        success = write_json_file(r"./conf/treeList.json", data.tree_data)
        if not success:
            raise HTTPException(status_code=500, detail="保存JSON文件失败")

        # 更新数据库
        await db_manager.insert_categories_from_json(data.tree_data)

        return format_response(
            message="类别树保存成功"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"保存类别树失败: {str(e)}")

@router.get("/getArmExpsByName", summary="获取样本列表")
async def get_arm_exps_by_name(
    category_path: str = Query(..., description="层级目录"),
    page: int = Query(1, ge=1, description="页码"),
    limit: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    获取样本列表,传递层级目录，分页返回样本列表（文件名、路径、上传时间、id）
    """
    try:
        offset = (page - 1) * limit
        samples = await db_manager.get_samples_by_category_path(
            category_path=category_path,
            limit=limit,
            offset=offset
        )

        # 转换数据格式以匹配前端期望
        formatted_samples = []
        for sample in samples:
            formatted_sample = {
                "id": sample.get("id"),
                "name": sample.get("name"),
                "uploadTime": sample.get("created_at"),  # 转换字段名
                "file_path": sample.get("file_path"),
                "description": sample.get("description"),
                "category_id": sample.get("category_id")
            }
            formatted_samples.append(formatted_sample)

        # 获取该分类下的总样本数（用于分页）
        try:
            category_id = int(category_path) if category_path.isdigit() else None
            if category_id:
                # 获取该分类的总数
                all_samples = await db_manager.get_samples_by_category_path(
                    category_path=category_path,
                    limit=10000,  # 获取大量数据来计算总数
                    offset=0
                )
                total_count = len(all_samples)
            else:
                total_count = len(formatted_samples)
        except:
            total_count = len(formatted_samples)

        return format_response(
            data={
                "list": formatted_samples,  # 改为list以匹配前端期望
                "total": total_count,       # 使用真实总数
                "page": page,
                "limit": limit,
                "category_path": category_path
            },
            message="获取样本列表成功"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取样本列表失败: {str(e)}")

@router.post("/delArmExpsByName", summary="删除样本")
async def del_arm_exps_by_name(data: Dict):
    """
    删除样本，传递id，在数据库获取路径，删除文件
    """
    try:
        sample_id = data.get("id")
        if not sample_id:
            raise HTTPException(status_code=400, detail="缺少样本ID")

        # 获取样本信息
        sample = await db_manager.get_sample_by_id(sample_id)
        if not sample:
            raise HTTPException(status_code=404, detail="样本不存在")

        # 删除文件
        if sample.get("file_path"):
            delete_file_safely(sample["file_path"])
            # 同时删除标注文件
            annotation_path = get_annotation_file_path(sample["file_path"])
            if annotation_path:
                delete_file_safely(annotation_path)

        # 删除数据库记录
        success = await db_manager.delete_sample(sample_id)
        if not success:
            raise HTTPException(status_code=500, detail="删除数据库记录失败")

        return format_response(
            message="样本删除成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除样本失败: {str(e)}")

@router.post("/delArmExpsBatch", summary="批量删除样本")
async def del_arm_exps_batch(data: BatchDelete):
    """
    批量删除，传递id数组，在数据库遍历查询，删除文件
    """
    try:
        deleted_count = 0

        for sample_id in data.ids:
            # 获取样本信息
            sample = await db_manager.get_sample_by_id(sample_id)
            if sample and sample.get("file_path"):
                # 删除文件
                delete_file_safely(sample["file_path"])
                # 删除标注文件
                annotation_path = get_annotation_file_path(sample["file_path"])
                if annotation_path:
                    delete_file_safely(annotation_path)

        # 批量删除数据库记录
        deleted_count = await db_manager.delete_samples_batch(data.ids)

        return format_response(
            data={"deleted_count": deleted_count},
            message=f"成功删除{deleted_count}个样本"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")

@router.get("/getArmTXTByName", summary="获取标注文件内容")
async def get_arm_txt_by_name(id: int = Query(..., description="样本ID")):
    """
    查看样本详情，传递id，在数据库获取路径，读取文件，调用labelimg
    """
    try:
        # 获取样本信息
        sample = await db_manager.get_sample_by_id(id)
        if not sample:
            raise HTTPException(status_code=404, detail="样本不存在")

        # 获取标注文件路径
        if not sample.get("file_path"):
            raise HTTPException(status_code=400, detail="样本没有关联文件")

        annotation_path = get_annotation_file_path(sample["file_path"])
        if not annotation_path:
            raise HTTPException(status_code=400, detail="无法确定标注文件路径")

        # 读取标注文件内容
        content = read_text_file(annotation_path)

        return format_response(
            data={
                "sample": sample,
                "annotation_content": content,
                "annotation_path": annotation_path
            },
            message="获取标注文件成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取标注文件失败: {str(e)}")

@router.post("/addArmExps", summary="添加样本")
async def add_arm_exps(
    category_path: str = Form(..., description="层级目录"),
    files: List[UploadFile] = File(..., description="上传的文件列表")
):
    """
    添加样本，传递层级目录，传递文件数据，保存到对应目录，更新数据库
    """
    try:
        # 将字符串类型的category_path转换为整数类型的category_id
        try:
            category_id = int(category_path) if category_path.isdigit() else None
        except (ValueError, AttributeError):
            category_id = None

        # 构建文件保存目录
        upload_dir = f"workspace/Samples/{category_path}"
        os.makedirs(upload_dir, exist_ok=True)

        uploaded_files = []
        image_count = 0
        annotation_count = 0

        # 处理每个上传的文件
        for file in files:
            # 验证文件类型
            if not is_valid_upload_file(file.filename):
                continue  # 跳过不支持的文件类型

            # 构建文件保存路径
            file_path = os.path.join(upload_dir, file.filename)

            # 保存文件
            file_content = await file.read()
            success = save_uploaded_file(file_content, file_path)
            if not success:
                continue  # 跳过保存失败的文件

            # 根据文件类型决定是否保存到数据库
            if is_image_file(file.filename):
                # 图片文件：保存到数据库作为样本
                sample_id = await db_manager.insert_sample(
                    name=file.filename,
                    category_id=category_id,
                    file_path=file_path,
                    description=f"上传的图片样本: {file.filename}"
                )
                uploaded_files.append({
                    "id": sample_id,
                    "filename": file.filename,
                    "file_path": file_path,
                    "type": "image"
                })
                image_count += 1
            else:
                # txt文件：只保存文件，不保存到数据库
                uploaded_files.append({
                    "filename": file.filename,
                    "file_path": file_path,
                    "type": "annotation"
                })
                annotation_count += 1

        return format_response(
            data={
                "uploaded_files": uploaded_files,
                "image_count": image_count,
                "annotation_count": annotation_count,
                "total_count": len(uploaded_files)
            },
            message=f"上传完成：{image_count}个图片样本，{annotation_count}个标注文件"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加样本失败: {str(e)}")

@router.post("/renameArmExp", summary="重命名样本")
async def rename_arm_exp(data: SampleRename):
    """
    重命名样本，传递id和新名称，更新数据库和文件系统
    """
    try:
        # 获取样本信息
        sample = await db_manager.get_sample_by_id(data.id)
        if not sample:
            raise HTTPException(status_code=404, detail="样本不存在")

        old_file_path = sample.get("file_path")
        if not old_file_path:
            # 如果没有文件路径，只更新数据库
            success = await db_manager.update_sample(
                sample_id=data.id,
                name=data.new_name
            )
            if not success:
                raise HTTPException(status_code=404, detail="样本不存在")

            return format_response(
                message="样本重命名成功"
            )

        # 计算新的文件路径
        old_dir = os.path.dirname(old_file_path)
        old_name = os.path.basename(old_file_path)
        old_extension = os.path.splitext(old_name)[1]

        # 检查新名称是否已经包含扩展名
        _, new_extension = os.path.splitext(data.new_name)
        if new_extension:
            # 用户输入已包含扩展名，直接使用
            final_name = data.new_name
        else:
            # 用户输入不包含扩展名，添加原文件的扩展名
            final_name = f"{data.new_name}{old_extension}"

        new_file_path = os.path.join(old_dir, final_name)

        # 重命名图片文件
        image_rename_success = True
        if os.path.exists(old_file_path):
            image_rename_success = rename_file_safely(old_file_path, new_file_path)

        # 重命名标注文件（如果存在）
        old_annotation_path = get_annotation_file_path(old_file_path)
        annotation_rename_success = True
        if os.path.exists(old_annotation_path):
            new_annotation_path = get_annotation_file_path(new_file_path)
            annotation_rename_success = rename_file_safely(old_annotation_path, new_annotation_path)

        # 如果文件重命名失败，回滚操作
        if not image_rename_success:
            raise HTTPException(status_code=500, detail="图片文件重命名失败")

        if not annotation_rename_success:
            # 回滚图片文件重命名
            if os.path.exists(new_file_path):
                rename_file_safely(new_file_path, old_file_path)
            raise HTTPException(status_code=500, detail="标注文件重命名失败")

        # 更新数据库
        success = await db_manager.update_sample(
            sample_id=data.id,
            name=data.new_name,
            file_path=new_file_path
        )

        if not success:
            # 回滚文件重命名
            if os.path.exists(new_file_path):
                rename_file_safely(new_file_path, old_file_path)
            new_annotation_path = get_annotation_file_path(new_file_path)
            if os.path.exists(new_annotation_path):
                rename_file_safely(new_annotation_path, old_annotation_path)
            raise HTTPException(status_code=404, detail="样本不存在")

        return format_response(
            message="样本和文件重命名成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重命名失败: {str(e)}")

@router.get("/viewArmExp", summary="查看样本")
async def view_arm_exp(id: int = Query(..., description="样本ID")):
    """
    查看样本，传递id，在数据库获取路径，读取标注文件，并返回前端
    """
    try:
        # 获取样本信息
        sample = await db_manager.get_sample_by_id(id)
        if not sample:
            raise HTTPException(status_code=404, detail="样本不存在")

        # 获取标注文件内容
        annotation_content = None
        annotation_path = None

        if sample.get("file_path"):
            annotation_path = get_annotation_file_path(sample["file_path"])
            if annotation_path:
                annotation_content = read_text_file(annotation_path)

        # 构建图片URL
        image_url = None
        if sample.get("file_path"):
            # 将文件路径转换为静态文件URL
            file_path = sample["file_path"]
            if file_path.startswith("workspace/") or file_path.startswith("workspace\\"):
                relative_path = file_path[10:]  # 移除 "workspace/" 或 "workspace\" 前缀
            else:
                relative_path = file_path

            # 确保URL使用正斜杠
            relative_path = relative_path.replace("\\", "/")
            image_url = f"http://127.0.0.1:8083/workspace/{relative_path}"

        return format_response(
            data={
                "sample": sample,
                "image_url": image_url,
                "annotation_content": annotation_content,
                "annotation_path": annotation_path
            },
            message="获取样本详情成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"查看样本失败: {str(e)}")

@router.get("/searchArmExps", summary="搜索样本")
async def search_arm_exps(
    keyword: str = Query(..., description="搜索关键词"),
    pageIndex: int = Query(1, ge=1, description="当前分页数"),
    pageSize: int = Query(20, ge=1, le=100, description="每页数量")
):
    """
    搜索样本，传递搜索词，当前分页数，在数据库中模糊查询，返回样本列表
    """
    try:
        offset = (pageIndex - 1) * pageSize
        samples = await db_manager.search_samples(
            search_term=keyword,
            limit=pageSize,
            offset=offset
        )

        # 转换数据格式以匹配前端期望
        formatted_samples = []
        for sample in samples:
            formatted_sample = {
                "id": sample.get("id"),
                "name": sample.get("name"),
                "uploadTime": sample.get("created_at"),  # 转换字段名
                "file_path": sample.get("file_path"),
                "description": sample.get("description"),
                "category_id": sample.get("category_id")
            }
            formatted_samples.append(formatted_sample)

        # 获取搜索结果的总数（用于分页）
        try:
            # 获取所有匹配的结果来计算总数
            all_samples = await db_manager.search_samples(
                search_term=keyword,
                limit=10000,  # 获取大量数据来计算总数
                offset=0
            )
            total_count = len(all_samples)
        except:
            total_count = len(formatted_samples)

        return format_response(
            data={
                "list": formatted_samples,  # 改为list以匹配前端期望
                "total": total_count,       # 使用真实总数
                "page": pageIndex,
                "limit": pageSize,
                "keyword": keyword
            },
            message="搜索样本成功"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"搜索失败: {str(e)}")

# ==================== 新增的类别管理接口 ====================

@router.post("/createCategory", summary="创建新类别")
async def create_category(data: Dict):
    """
    创建新类别，支持从检测界面自动创建类别
    """
    try:
        category_name = data.get("name")
        parent_id = data.get("parent_id")
        description = data.get("description", f"自动创建的类别: {category_name}")

        if not category_name:
            raise HTTPException(status_code=400, detail="类别名称不能为空")

        # 读取当前类别树
        tree_data = read_json_file(r"./conf/treeList.json") or []

        # 生成新的类别ID
        max_id = 0
        def find_max_id(nodes):
            nonlocal max_id
            for node in nodes:
                if isinstance(node.get('id'), int):
                    max_id = max(max_id, node['id'])
                if node.get('children'):
                    find_max_id(node['children'])

        find_max_id(tree_data)
        new_id = max_id + 1

        # 创建新类别节点
        new_category = {
            "id": new_id,
            "label": category_name,
            "name": category_name,
            "children": []
        }

        # 如果有父类别，添加到父类别下，否则添加为根类别
        if parent_id:
            def add_to_parent(nodes, target_id, new_node):
                for node in nodes:
                    if node.get('id') == target_id:
                        if 'children' not in node:
                            node['children'] = []
                        node['children'].append(new_node)
                        return True
                    if node.get('children') and add_to_parent(node['children'], target_id, new_node):
                        return True
                return False

            if not add_to_parent(tree_data, parent_id, new_category):
                raise HTTPException(status_code=404, detail="父类别不存在")
        else:
            tree_data.append(new_category)

        # 保存更新后的类别树
        success = write_json_file(r"./conf/treeList.json", tree_data)
        if not success:
            raise HTTPException(status_code=500, detail="保存类别树失败")

        # 更新数据库
        await db_manager.insert_categories_from_json(tree_data)

        return format_response(
            data={
                "id": new_id,
                "name": category_name,
                "label": category_name
            },
            message=f"类别 '{category_name}' 创建成功"
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建类别失败: {str(e)}")

@router.post("/refreshSampleLibrary", summary="刷新样本库")
async def refresh_sample_library():
    """
    刷新样本库数据，用于同步检测界面的更改
    """
    try:
        # 重新加载类别树并更新数据库
        tree_data = read_json_file(r"./conf/treeList.json")
        if tree_data:
            await db_manager.insert_categories_from_json(tree_data)

        return format_response(
            message="样本库数据刷新成功"
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"刷新样本库失败: {str(e)}")

