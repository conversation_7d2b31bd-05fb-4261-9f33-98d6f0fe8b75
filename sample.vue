<template>
  <div class="sample">
    <HeaderComponent title="样本管理" :showBackButton="true" @back="goBack" />
    <div class="content">
      <!-- 左侧区域 -->
      <div class="left">

        <!-- 搜索框 -->
        <div class="search-section">
          <el-input v-model="searchQuery" placeholder="搜索类别..." clearable @input="handleSearch">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
        </div>

        <!-- 类别树 -->
        <div class="category-tree">
          <div v-if="loading.categoryTree" class="loading-container">
            <el-icon class="is-loading">
              <Loading />
            </el-icon>
            <span>加载类别树中...</span>
          </div>
          <el-tree v-else :data="treeData" :props="treeProps" :filter-node-method="filterNode" ref="treeRef"
            node-key="id" :expand-on-click-node="false" :default-expanded-keys="[1]" @node-click="handleNodeClick">
            <template #default="{ node, data }">
              <span class="tree-node">
                <el-icon v-if="data.children && data.children.length > 0">
                  <Folder />
                </el-icon>
                <el-icon v-else>
                  <Document />
                </el-icon>
                <span>{{ node.label }}</span>
              </span>
            </template>
          </el-tree>
        </div>
        <!-- 编辑类别按钮 -->
        <div class="edit-category-section">
          <el-button type="warning" @click="openCategoryDialog" style="width: 100%; margin-bottom: 15px;">
            <el-icon>
              <Edit />
            </el-icon>
            编辑类别
          </el-button>
        </div>
      </div>

      <!-- 中间区域 -->
      <div class="center">
        <!-- 功能框 -->
        <div class="function-bar">
          <div class="function-left">
            <el-input v-model="sampleSearchQuery" placeholder="搜索样本..." style="width: 200px; margin-right: 10px;"
              clearable @keyup.enter="searchSamples" @clear="handleSearchClear">
              <template #prefix>
                <el-icon>
                  <Search />
                </el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="searchSamples">搜索</el-button>
          </div>
          <div class="function-right">
            <el-button type="success" @click="addSample" :loading="loading.operations">
              <el-icon>
                <Plus />
              </el-icon>
              增加样本
            </el-button>
            <el-button type="danger" @click="deleteSamples" :disabled="selectedSamples.length === 0"
              :loading="loading.operations">
              <el-icon>
                <Delete />
              </el-icon>
              删除样本
            </el-button>
          </div>
        </div>

        <!-- 样本列表 -->
        <div class="sample-list">
          <el-table :data="paginatedSamples" @selection-change="handleSelectionChange" style="width: 100%" height="90%"
            v-loading="loading.sampleList" element-loading-text="加载样本列表中..." :scroll-x="true" stripe border>
            <el-table-column type="selection" width="50" fixed="left"></el-table-column>
            <el-table-column prop="name" label="样本名称" min-width="150" show-overflow-tooltip></el-table-column>
            <el-table-column prop="uploadTime" label="上传时间" width="160" show-overflow-tooltip></el-table-column>
            <el-table-column label="操作" width="320" fixed="right">
              <template #default="scope">
                <el-button size="small" @click="viewAnnotation(scope.row)">标注详情</el-button>
                <el-button size="small" type="danger" @click="deleteSample(scope.row)">删除</el-button>
                <el-button size="small" type="primary" @click="viewSample(scope.row)">查看</el-button>
                <el-button size="small" type="warning" @click="renameSample(scope.row)">重命名</el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="pagination-container">
            <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize"
              :page-sizes="[10, 20, 50, 100]" :total="totalSamples" layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange" @current-change="handleCurrentChange" :prev-text="'上一页'"
              :next-text="'下一页'" />
          </div>
        </div>
      </div>

      <!-- 右侧区域 -->
      <div class="right">
        <!-- 图片展示框 -->
        <div class="image-display">
          <div class="image-header">
            <span class="image-title">图片预览</span>
            <div class="image-controls">
              <el-button size="small" :type="showAnnotations ? 'primary' : 'default'" @click="toggleAnnotations"
                :disabled="!currentImage || !currentAnnotations.length">
                {{ showAnnotations ? '隐藏标注' : '显示标注' }}
              </el-button>
              <el-button size="small" @click="openImageViewer" :disabled="!currentImage">
                放大查看
              </el-button>
            </div>
          </div>
          <div class="image-container">
            <el-image v-if="currentImage" :src="currentImage" fit="contain"
              style="width: 100%; height: 100%; cursor: pointer;" @click="openImageViewer" />
            <div v-else class="no-image">
              <el-icon size="48">
                <Picture />
              </el-icon>
              <p>请选择样本查看</p>
            </div>
            <!-- 标注层 -->
            <canvas v-if="showAnnotations && currentImage" ref="annotationCanvas" class="annotation-canvas"></canvas>
          </div>
        </div>

        <!-- 介绍说明 -->
        <div class="description">
          <h4>标注说明</h4>
          <div class="annotation-info-container">
            <div class="annotation-section">
              <div class="annotation-title">1. 格式</div>
              <div class="annotation-content">
                标注文件格式采用标准YOLO格式，文件名称为：<span class="highlight-text">{图片名称}.txt</span>
              </div>
            </div>

            <div class="annotation-section">
              <div class="annotation-title">2. 内容</div>
              <div class="annotation-content">
                标注文件内容为：<span class="highlight-text">{类别} {x_center} {y_center} {width} {height}</span>
              </div>
              <div class="annotation-content">
                数值均作归一化处理
              </div>
            </div>

            <div class="annotation-section">
              <div class="annotation-title">3. 类别说明</div>
              <div v-if="categoryList.length > 0" class="class-grid">
                <div v-for="category in categoryList" :key="category.id" class="class-item">
                  <span class="class-color" :style="{ backgroundColor: category.color }"></span>
                  <span class="class-name">
                    {{ category.index }}-{{ category.name }}
                  </span>
                  <span class="class-count" :class="{ 'zero-count': category.count === 0 }">
                    ({{ category.count }}个样本)
                  </span>
                </div>
              </div>
              <div v-else class="no-categories">
                <el-empty description="暂无类别数据" :image-size="60" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 编辑类别对话框 -->
    <el-dialog v-model="categoryDialogVisible" title="编辑类别" width="600px" :before-close="handleDialogClose">
      <div class="category-editor">
        <!-- 添加新类别 -->
        <div class="add-category-section">
          <h4>添加新类别</h4>
          <div class="add-form">
            <el-select v-model="newCategory.parentId" placeholder="选择父类别（可选）" clearable
              style="width: 200px; margin-right: 10px;">
              <el-option v-for="category in flatCategories" :key="category.id" :label="category.label"
                :value="category.id" />
            </el-select>
            <el-input v-model="newCategory.name" placeholder="输入类别名称" style="width: 200px; margin-right: 10px;"
              @keyup.enter="addCategory" />
            <el-button type="primary" @click="addCategory">添加</el-button>
          </div>
        </div>

        <!-- 类别列表 -->
        <div class="category-list-section">
          <h4>类别列表</h4>
          <el-tree :data="editableTreeData" :props="treeProps" node-key="id" :expand-on-click-node="false"
            :default-expand-all="true" class="category-edit-tree">
            <template #default="{ node, data }">
              <div class="tree-node-editor">
                <el-icon v-if="data.children && data.children.length > 0">
                  <Folder />
                </el-icon>
                <el-icon v-else>
                  <Document />
                </el-icon>

                <el-input v-if="data.editing" v-model="data.tempLabel" size="small" style="width: 150px; margin: 0 8px;"
                  @blur="saveEdit(data)" @keyup.enter="saveEdit(data)" @keyup.esc="cancelEdit(data)" ref="editInput" />
                <span v-else class="node-label">{{ node.label }}</span>

                <div class="node-actions">
                  <el-button v-if="!data.editing" size="small" type="primary" @click="startEdit(data)">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                  <el-button v-if="data.editing" size="small" type="success" @click="saveEdit(data)">
                    <el-icon>
                      <Check />
                    </el-icon>
                  </el-button>
                  <el-button v-if="data.editing" size="small" type="info" @click="cancelEdit(data)">
                    <el-icon>
                      <Close />
                    </el-icon>
                  </el-button>
                  <el-button v-if="!data.editing" size="small" type="danger" @click="deleteCategory(data)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCategoryEdit">取消</el-button>
          <el-button type="primary" @click="saveCategoryChanges">确认</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 自定义图像查看器 -->
    <el-dialog v-model="imageViewerVisible" title="图像查看器" width="90%" :before-close="closeImageViewer"
      class="image-viewer-dialog">
      <div class="viewer-header">
        <div class="viewer-controls">
          <el-button size="small" :type="viewerShowAnnotations ? 'primary' : 'default'" @click="toggleViewerAnnotations"
            :disabled="!currentAnnotations.length">
            <el-icon>
              <View />
            </el-icon>
            {{ viewerShowAnnotations ? '隐藏标注' : '显示标注' }}
          </el-button>
          <el-button size="small" @click="resetViewerView">
            <el-icon>
              <Refresh />
            </el-icon>
            重置视图
          </el-button>
          <div class="zoom-info">
            <el-icon>
              <ZoomIn />
            </el-icon>
            <span>{{ Math.round(viewerImageScale * 100) }}%</span>
          </div>
        </div>
      </div>
      <div class="viewer-container" @wheel="handleViewerWheel" @mousedown="handleViewerMouseDown"
        @mousemove="handleViewerMouseMove" @mouseup="handleViewerMouseUp" @mouseleave="handleViewerMouseUp">
        <img v-if="currentImage" :src="currentImage" ref="viewerImage" class="viewer-image" :style="viewerImageStyle"
          @dragstart.prevent />
        <!-- 放大模式标注层 -->
        <canvas v-if="viewerShowAnnotations && currentImage" ref="viewerAnnotationCanvas"
          class="viewer-annotation-canvas"></canvas>
      </div>
    </el-dialog>

    <!-- 上传进度对话框 -->
    <el-dialog v-model="uploadProgress.show" title="文件上传进度" width="600px" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="uploadProgress.status === 'completed' || uploadProgress.status === 'error'">
      <div class="upload-progress-content">
        <!-- 总体进度 -->
        <div class="progress-section">
          <div class="progress-header">
            <span class="progress-title">总体进度</span>
            <span class="progress-stats">{{ uploadProgress.current }} / {{ uploadProgress.total }}</span>
          </div>
          <el-progress
            :percentage="uploadProgress.percentage"
            :status="uploadProgress.status === 'error' ? 'exception' : (uploadProgress.status === 'completed' ? 'success' : '')"
            :stroke-width="8"
            text-inside
          />
        </div>

        <!-- 批次进度 -->
        <div v-if="uploadProgress.totalBatches > 1" class="progress-section">
          <div class="progress-header">
            <span class="progress-title">批次进度</span>
            <span class="progress-stats">{{ uploadProgress.currentBatch }} / {{ uploadProgress.totalBatches }}</span>
          </div>
          <el-progress
            :percentage="uploadProgress.totalBatches > 0 ? Math.round((uploadProgress.currentBatch / uploadProgress.totalBatches) * 100) : 0"
            :stroke-width="6"
          />
        </div>

        <!-- 状态信息 -->
        <div class="progress-section">
          <div class="status-message">
            <el-icon v-if="uploadProgress.status === 'uploading'" class="is-loading">
              <Loading />
            </el-icon>
            <el-icon v-else-if="uploadProgress.status === 'completed'" style="color: #67c23a;">
              <SuccessFilled />
            </el-icon>
            <el-icon v-else-if="uploadProgress.status === 'error'" style="color: #f56c6c;">
              <CircleCloseFilled />
            </el-icon>
            <span>{{ uploadProgress.message }}</span>
          </div>
        </div>

        <!-- 性能指标 -->
        <div v-if="uploadProgress.status === 'uploading' && uploadProgress.uploadSpeed > 0" class="progress-section">
          <div class="performance-metrics">
            <div class="metric-item">
              <span class="metric-label">上传速度:</span>
              <span class="metric-value">{{ Math.round(uploadProgress.uploadSpeed) }} 文件/秒</span>
            </div>
            <div v-if="uploadProgress.estimatedTimeRemaining" class="metric-item">
              <span class="metric-label">预计剩余:</span>
              <span class="metric-value">{{ uploadProgress.estimatedTimeRemaining }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">数据量:</span>
              <span class="metric-value">{{ formatFileSize(uploadProgress.results.uploadedSize) }} / {{ formatFileSize(uploadProgress.results.totalSize) }}</span>
            </div>
          </div>
        </div>

        <!-- 结果统计 -->
        <div v-if="uploadProgress.status === 'completed' || uploadProgress.status === 'cancelled'" class="progress-section">
          <div class="result-stats">
            <div class="stat-item">
              <span class="stat-label">总文件数:</span>
              <span class="stat-value">{{ uploadProgress.results.totalFiles }}</span>
            </div>
            <div class="stat-item success">
              <span class="stat-label">成功:</span>
              <span class="stat-value">{{ uploadProgress.results.successCount }}</span>
            </div>
            <div v-if="uploadProgress.results.failedCount > 0" class="stat-item error">
              <span class="stat-label">失败:</span>
              <span class="stat-value">{{ uploadProgress.results.failedCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">图片:</span>
              <span class="stat-value">{{ uploadProgress.results.imageCount }}</span>
            </div>
            <div class="stat-item">
              <span class="stat-label">标注:</span>
              <span class="stat-value">{{ uploadProgress.results.annotationCount }}</span>
            </div>
          </div>

          <!-- 失败批次详情 -->
          <div v-if="uploadProgress.results.failedBatches.length > 0" class="failed-batches">
            <h5>失败批次详情:</h5>
            <div class="failed-batch-list">
              <div v-for="batch in uploadProgress.results.failedBatches" :key="batch.index" class="failed-batch-item">
                <span class="batch-info">批次 {{ batch.index }} ({{ batch.files.length }} 个文件)</span>
                <span class="batch-error">{{ batch.error }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button v-if="uploadProgress.status === 'uploading'"
            type="danger"
            @click="cancelUpload">
            取消上传
          </el-button>
          <el-button v-if="uploadProgress.status === 'completed' || uploadProgress.status === 'error' || uploadProgress.status === 'cancelled'"
            @click="uploadProgress.show = false">
            关闭
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 标注详情对话框 -->
    <el-dialog v-model="annotationDialogVisible" title="标注详情" width="80%" class="annotation-dialog">
      <div v-if="currentAnnotationData" class="annotation-content">
        <!-- 文件信息 -->
        <div class="annotation-info">
          <h4>文件信息</h4>
          <p><strong>样本名称：</strong>{{ currentAnnotationData.sample?.name }}</p>
          <p><strong>标注文件路径：</strong>{{ currentAnnotationData.annotation_path }}</p>
        </div>

        <!-- 原始内容 -->
        <div class="annotation-raw">
          <h4>原始标注内容</h4>
          <el-input v-model="currentAnnotationData.annotation_content" type="textarea" :rows="8" readonly
            placeholder="无标注内容" />
          <div class="raw-actions">
            <el-button size="small" @click="copyAnnotationContent">复制内容</el-button>
          </div>
        </div>

        <!-- 解析后的表格 -->
        <div v-if="parsedAnnotations.length > 0" class="annotation-table">
          <h4>解析后的标注数据（YOLO格式）</h4>
          <el-table :data="parsedAnnotations" border stripe>
            <el-table-column prop="lineNumber" label="行号" width="80" align="center" />
            <el-table-column prop="classId" label="类别ID" width="100" align="center" />
            <el-table-column prop="xCenter" label="中心X坐标" width="120" align="center" />
            <el-table-column prop="yCenter" label="中心Y坐标" width="120" align="center" />
            <el-table-column prop="width" label="宽度" width="120" align="center" />
            <el-table-column prop="height" label="高度" width="120" align="center" />
            <el-table-column prop="rawLine" label="原始行" min-width="200" show-overflow-tooltip />
          </el-table>
        </div>

        <!-- 无标注数据提示 -->
        <div v-else class="no-annotation">
          <el-empty description="该样本没有标注数据" />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="annotationDialogVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import HeaderComponent from '@/components/header.vue'
import { ZoomIn, View, Search, Folder, Document, Plus, Delete, Picture, Edit, Check, Close, Loading, Refresh, SuccessFilled, CircleCloseFilled } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
// 导入API接口
import {
  APIGetCategoryTree,
  APISaveCategoryTree,
  APIGetSamplesListByName,
  APIDelSamples,
  APIDelSamplesBatch,
  APIGetTXTContentByName,
  APIAddSamples,
  APIRenameSample,
  APIViewSample,
  APISearchSamples
} from '@/api/api.js'

export default {
  name: 'SampleView',
  components: {
    HeaderComponent,
    ZoomIn,
    View,
    Search,
    Folder,
    Document,
    Plus,
    Delete,
    Picture,
    Edit,
    Check,
    Close,
    Loading,
    Refresh,
    SuccessFilled,
    CircleCloseFilled
  },
  data() {
    return {
      // 加载状态管理
      loading: {
        categoryTree: false,
        sampleList: false,
        sampleDetail: false,
        operations: false
      },

      // 错误状态管理
      error: {
        message: '',
        show: false
      },

      // 当前选中的类别
      currentCategory: null,

      // 搜索相关
      searchQuery: '',
      sampleSearchQuery: '',
      isInSearchMode: false, // 标识当前是否在搜索状态

      // 分页相关
      currentPage: 1,
      pageSize: 10,
      totalSamples: 0,

      // 选择相关
      selectedSamples: [],

      // 图片展示
      currentImage: '',
      currentSample: null,
      currentAnnotations: [],
      showAnnotations: false,

      // 图片查看器状态（预览模式已移除缩放功能）
      imageScale: 1,
      imagePosition: { x: 0, y: 0 },
      isDragging: false,
      dragStart: { x: 0, y: 0 },

      // 自定义图像查看器状态
      imageViewerVisible: false,
      viewerShowAnnotations: false,
      viewerImageScale: 1,
      viewerImagePosition: { x: 0, y: 0 },
      viewerIsDragging: false,
      viewerDragStart: { x: 0, y: 0 },

      // 防抖定时器
      annotationRedrawTimer: null,

      // 标注详情对话框状态
      annotationDialogVisible: false,
      currentAnnotationData: null,
      parsedAnnotations: [],

      // 类别编辑相关数据
      categoryDialogVisible: false,
      editableTreeData: [],
      newCategory: {
        name: '',
        parentId: null
      },
      nextCategoryId: 1000,
      hasChanges: false,

      // 树形数据
      treeData: [
        // {
        //   id: 1,
        //   label: '类别1',
        //   children: [
        //     { id: 11, label: '子类别1-1' },
        //     { id: 12, label: '子类别1-2' }
        //   ]
        // },
        // {
        //   id: 2,
        //   label: '类别2',
        //   children: [
        //     { id: 21, label: '子类别2-1' }
        //   ]
        // }
      ],
      treeProps: {
        children: 'children',
        label: 'label'
      },

      // 样本数据
      sampleList: [
        // { id: 1, name: '样本1', uploadTime: '2024-01-01 10:00:00' },
        // { id: 2, name: '样本2', uploadTime: '2024-01-02 11:00:00' },
        // { id: 3, name: '样本3', uploadTime: '2024-01-03 12:00:00' },
        // { id: 4, name: '样本4', uploadTime: '2024-01-04 13:00:00' },
        // { id: 5, name: '样本5', uploadTime: '2024-01-05 14:00:00' }
      ],

      // 类别列表
      categoryList: [],

      // 类别颜色列表（用于循环分配）
      categoryColors: [
        '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7',
        '#DDA0DD', '#98D8C8', '#F7DC6F', '#BB8FCE', '#85C1E9',
        '#F8C471', '#82E0AA', '#F1948A', '#85C1E9', '#D7BDE2',
        '#A3E4D7', '#F9E79F', '#D5A6BD', '#AED6F1', '#A9DFBF'
      ],

      // 上传进度状态
      uploadProgress: {
        show: false,
        current: 0,
        total: 0,
        currentBatch: 0,
        totalBatches: 0,
        percentage: 0,
        status: 'ready', // ready, uploading, completed, error, cancelled
        message: '',
        cancelled: false,
        startTime: null,
        estimatedTimeRemaining: null,
        uploadSpeed: 0, // 文件/秒
        results: {
          totalFiles: 0,
          successCount: 0,
          failedCount: 0,
          imageCount: 0,
          annotationCount: 0,
          failedBatches: [],
          totalSize: 0,
          uploadedSize: 0
        }
      }
    }
  },
  computed: {
    // 扁平化类别数据，用于父类别选择
    flatCategories() {
      const flatten = (data, result = []) => {
        data.forEach(item => {
          result.push({ id: item.id, label: item.label })
          if (item.children && item.children.length > 0) {
            flatten(item.children, result)
          }
        })
        return result
      }
      return flatten(this.editableTreeData)
    },

    // 分页样本数据（后端已分页，直接返回）
    paginatedSamples() {
      return this.sampleList
    },

    // 查看器图片样式
    viewerImageStyle() {
      return {
        transform: `scale(${this.viewerImageScale}) translate(${this.viewerImagePosition.x / this.viewerImageScale}px, ${this.viewerImagePosition.y / this.viewerImageScale}px)`,
        cursor: this.viewerImageScale > 1 ? (this.viewerIsDragging ? 'grabbing' : 'grab') : 'default',
        transition: this.viewerIsDragging ? 'none' : 'transform 0.2s ease'
      }
    }
  },
  async mounted() {
    // 初始化类别树
    await this.initCategoryTree()
    // 初始化总数
    this.totalSamples = this.sampleList.length

    // 监听来自检测界面的样本库更新事件
    window.addEventListener('message', this.handleSampleLibraryUpdate)
  },

  beforeUnmount() {
    // 清理事件监听器
    window.removeEventListener('message', this.handleSampleLibraryUpdate)
  },
  methods: {
    // 通用错误处理方法
    handleError(error, defaultMessage = '操作失败') {
      console.error('API Error:', error)
      const message = error?.response?.data?.message || error?.message || defaultMessage
      ElMessage.error(message)
      this.error.message = message
      this.error.show = true
    },

    // 处理样本库更新事件
    handleSampleLibraryUpdate(event) {
      if (event.data && event.data.type === 'SAMPLE_LIBRARY_UPDATED') {
        console.log('收到样本库更新通知，来源:', event.data.source)

        // 刷新类别树
        this.initCategoryTree()

        // 如果当前有选中的类别，重新加载样本列表
        if (this.currentCategory) {
          this.loadSamplesByCategory(this.currentCategory)
        }

        ElMessage.success('样本库已同步更新')
      }
    },

    // 初始化类别树
    async initCategoryTree() {
      this.loading.categoryTree = true
      try {
        const response = await APIGetCategoryTree()
        if (response && response.status === 'success') {
          // 使用nextTick确保DOM更新的正确时机
          await this.$nextTick()
          console.log(response.data)
          // 转换API数据格式为Tree组件格式
          this.treeData = this.convertCategoryData(response.data || [])

          // 生成类别列表数据（用于类别说明）
          await this.generateCategoryList()
        } else {
          throw new Error(response?.message || '获取类别树失败')
        }
      } catch (error) {
        this.handleError(error, '获取类别树失败')
        // 保持原有的模拟数据作为fallback
      } finally {
        // 延迟关闭loading，避免DOM快速变化
        await this.$nextTick()
        this.loading.categoryTree = false
      }
    },

    // 转换类别数据格式
    convertCategoryData(apiData) {
      // 如果API返回的数据格式与组件需要的格式不同，在这里进行转换
      // 假设API返回格式需要转换为 {id, label, children} 格式
      if (!Array.isArray(apiData)) return this.treeData // 返回默认数据

      return apiData.map(item => ({
        id: item.id || item.categoryId,
        label: item.name || item.categoryName || item.label,
        children: item.children ? this.convertCategoryData(item.children) : []
      }))
    },

    // 分页方法
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1 // 重置到第一页
      // 重新加载当前类别的样本列表
      if (this.currentCategory) {
        this.loadSamplesByCategory(this.currentCategory)
      }
    },

    handleCurrentChange(val) {
      this.currentPage = val
      // 重新加载当前类别的样本列表
      if (this.currentCategory) {
        this.loadSamplesByCategory(this.currentCategory)
      }
    },

    // 搜索方法
    handleSearch() {
      this.$refs.treeRef.filter(this.searchQuery)
    },
    filterNode(value, data) {
      if (!value) return true
      return data.label.indexOf(value) !== -1
    },

    // 树节点点击
    async handleNodeClick(data) {
      console.log('选中类别:', data.label)
      this.currentCategory = data
      this.isInSearchMode = false // 清除搜索状态
      this.sampleSearchQuery = '' // 清空搜索关键词

      // 判断是否为子节点（叶子节点）
      const isLeafNode = !data.children || data.children.length === 0

      if (isLeafNode) {
        // 只有子节点才加载样本列表
        await this.loadSamplesByCategory(this.currentCategory)
      } else {
        // 父节点点击时给出提示
        ElMessage.info(`请选择 "${data.label}" 下的具体子类别来查看样本`)
      }
    },

    // 根据类别加载样本列表
    async loadSamplesByCategory(category) {
      this.loading.sampleList = true
      try {
        const params = {
          category_path: category.id,
          page: this.currentPage,
          limit: this.pageSize
        }

        const response = await APIGetSamplesListByName(params)
        if (response && response.status === 'success') {
          this.sampleList = response.data?.list || []
          this.totalSamples = response.data?.total || 0
        } else {
          throw new Error(response?.message || '获取样本列表失败')
        }
      } catch (error) {
        this.handleError(error, '获取样本列表失败')
        // 保持原有数据或清空列表
        this.sampleList = []
        this.totalSamples = 0
      } finally {
        this.loading.sampleList = false
      }
    },

    // 获取类别路径
    getCategoryPath(category) {
      // 根据类别数据构建路径，具体格式需要根据后端API要求调整
      return category.label || category.name || ''
    },

    // 样本操作
    async searchSamples() {
      if (!this.sampleSearchQuery.trim()) {
        ElMessage.warning('请输入搜索关键词')
        return
      }

      this.loading.sampleList = true
      try {
        const params = {
          keyword: this.sampleSearchQuery.trim(),
          pageIndex: this.currentPage,
          pageSize: this.pageSize
        }

        const response = await APISearchSamples(params)
        if (response && response.status === 'success') {
          this.sampleList = response.data?.list || []
          this.totalSamples = response.data?.total || 0
          this.currentCategory = null // 清空当前类别选择
          this.isInSearchMode = true // 设置为搜索状态
        } else {
          throw new Error(response?.message || '搜索样本失败')
        }
      } catch (error) {
        this.handleError(error, '搜索样本失败')
      } finally {
        this.loading.sampleList = false
      }
    },

    // 处理搜索框清空
    handleSearchClear() {
      this.isInSearchMode = false
      this.sampleList = []
      this.totalSamples = 0
      // 如果有选中的类别，重新加载该类别的样本
      if (this.currentCategory) {
        this.loadSamplesByCategory(this.currentCategory)
      }
    },
    addSample() {
      if (!this.currentCategory) {
        ElMessage.warning('请先选择一个类别')
        return
      }

      // 判断是否为叶子节点，只有叶子节点才能添加样本
      const isLeafNode = !this.currentCategory.children || this.currentCategory.children.length === 0
      if (!isLeafNode) {
        ElMessage.warning(`请选择 "${this.currentCategory.label}" 下的具体子类别来添加样本`)
        return
      }

      // 创建文件输入元素
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = 'image/*,.txt'

      input.onchange = async (event) => {
        const files = event.target.files
        if (!files || files.length === 0) return

        await this.uploadSamples(files)
      }

      input.click()
    },

    // 上传样本文件 - 支持大量文件分批上传
    async uploadSamples(files) {
      this.loading.operations = true

      // 将 FileList 转换为数组
      const filesArray = Array.from(files)

      // 初始化上传状态
      const totalSize = filesArray.reduce((sum, file) => sum + file.size, 0)

      this.uploadProgress = {
        show: true,
        current: 0,
        total: filesArray.length,
        currentBatch: 0,
        totalBatches: 0,
        percentage: 0,
        status: 'uploading',
        message: '准备上传...',
        cancelled: false,
        startTime: Date.now(),
        estimatedTimeRemaining: null,
        uploadSpeed: 0,
        results: {
          totalFiles: filesArray.length,
          successCount: 0,
          failedCount: 0,
          imageCount: 0,
          annotationCount: 0,
          failedBatches: [],
          totalSize: totalSize,
          uploadedSize: 0
        }
      }

      try {
        // 预处理：过滤和验证文件
        const validFiles = filesArray.filter(file => this.validateFileForUpload(file))
        const invalidCount = filesArray.length - validFiles.length

        if (invalidCount > 0) {
          ElMessage.warning(`跳过了${invalidCount}个无效文件`)
          this.uploadProgress.total = validFiles.length
          this.uploadProgress.results.totalFiles = validFiles.length
        }

        if (validFiles.length === 0) {
          throw new Error('没有有效的文件可以上传')
        }

        // 智能选择上传策略
        if (validFiles.length <= 50) {
          // 小批量文件，直接上传
          this.uploadProgress.message = '开始直接上传...'
          await this.uploadBatchWithValidation(validFiles, 0, 1)
        } else {
          // 大批量文件，分批上传
          this.uploadProgress.message = '开始分批上传...'
          await this.uploadLargeFileSet(validFiles)
        }

        // 上传完成后的处理
        await this.handleUploadComplete()

      } catch (error) {
        this.uploadProgress.status = 'error'
        this.uploadProgress.message = `上传失败: ${error.message || '未知错误'}`
        this.handleError(error, '上传样本失败')
      } finally {
        this.loading.operations = false
        // 根据上传结果决定隐藏时间
        const hideDelay = this.uploadProgress.status === 'error' ? 5000 : 3000
        setTimeout(() => {
          if (this.uploadProgress.status !== 'uploading') {
            this.uploadProgress.show = false
          }
        }, hideDelay)
      }
    },

    // 大文件集分批上传 - 针对20000+文件优化
    async uploadLargeFileSet(files) {
      // 动态调整批次大小，基于文件总数和平均文件大小
      const BATCH_SIZE = this.calculateOptimalBatchSize(files)
      const MAX_RETRIES = 3 // 最大重试次数
      const BATCH_DELAY = this.calculateBatchDelay(files.length) // 动态延迟

      const batches = []

      // 将文件分组
      for (let i = 0; i < files.length; i += BATCH_SIZE) {
        batches.push({
          files: files.slice(i, i + BATCH_SIZE),
          index: Math.floor(i / BATCH_SIZE),
          retryCount: 0
        })
      }

      this.uploadProgress.totalBatches = batches.length
      this.uploadProgress.message = `开始分批上传，共${batches.length}批，每批约${BATCH_SIZE}个文件`

      // 逐批上传，支持重试机制
      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex]

        // 检查是否被取消
        if (this.uploadProgress.cancelled) {
          this.uploadProgress.status = 'cancelled'
          this.uploadProgress.message = '上传已取消'
          return
        }

        this.uploadProgress.currentBatch = batchIndex + 1
        this.uploadProgress.message = `正在上传第${batchIndex + 1}/${batches.length}批文件... (${batch.files.length}个文件)`

        let uploadSuccess = false

        // 重试机制
        while (!uploadSuccess && batch.retryCount < MAX_RETRIES) {
          try {
            await this.uploadBatchWithValidation(batch.files, batchIndex, batches.length)
            uploadSuccess = true

            // 更新总体进度
            this.uploadProgress.current = Math.min(
              this.uploadProgress.current + batch.files.length,
              this.uploadProgress.total
            )
            this.uploadProgress.percentage = Math.round(
              (this.uploadProgress.current / this.uploadProgress.total) * 100
            )

            // 更新上传大小
            const batchFilesArray = Array.isArray(batch.files) ? batch.files : Array.from(batch.files)
            const batchSize = batchFilesArray.reduce((sum, file) => sum + file.size, 0)
            this.uploadProgress.results.uploadedSize += batchSize

            // 计算上传速度和预估剩余时间
            this.updateUploadMetrics()

            console.log(`批次${batchIndex + 1}上传成功，已完成${this.uploadProgress.current}/${this.uploadProgress.total}个文件`)

          } catch (error) {
            batch.retryCount++
            console.error(`第${batchIndex + 1}批上传失败 (第${batch.retryCount}次尝试):`, error)

            if (batch.retryCount < MAX_RETRIES) {
              this.uploadProgress.message = `第${batchIndex + 1}批上传失败，正在重试... (${batch.retryCount}/${MAX_RETRIES})`
              // 重试前等待更长时间
              await new Promise(resolve => setTimeout(resolve, 2000 * batch.retryCount))
            } else {
              // 达到最大重试次数，记录失败
              this.uploadProgress.results.failedCount += batch.files.length
              this.uploadProgress.results.failedBatches.push({
                index: batchIndex + 1,
                files: batch.files,
                error: error.message,
                retryCount: batch.retryCount
              })
              console.error(`第${batchIndex + 1}批最终上传失败，已重试${batch.retryCount}次`)
            }
          }
        }

        // 批次间延迟，避免服务器压力过大
        if (batchIndex < batches.length - 1 && !this.uploadProgress.cancelled) {
          // 内存清理和垃圾回收
          const needExtraDelay = this.performMemoryCleanup()
          const actualDelay = needExtraDelay ? BATCH_DELAY * 2 : BATCH_DELAY
          await new Promise(resolve => setTimeout(resolve, actualDelay))
        }
      }
    },

    // 取消上传
    cancelUpload() {
      this.uploadProgress.cancelled = true
      this.uploadProgress.status = 'cancelled'
      this.uploadProgress.message = '正在取消上传...'
    },

    // 计算最优批次大小
    calculateOptimalBatchSize(files) {
      const filesArray = Array.isArray(files) ? files : Array.from(files)
      const totalFiles = filesArray.length
      const avgFileSize = filesArray.reduce((sum, file) => sum + file.size, 0) / totalFiles

      // 基于文件数量和平均大小动态调整
      if (totalFiles > 10000) {
        // 超大批量：使用较小批次，确保稳定性
        return avgFileSize > 1024 * 1024 ? 20 : 30 // 大文件20个，小文件30个
      } else if (totalFiles > 5000) {
        // 大批量：中等批次大小
        return avgFileSize > 1024 * 1024 ? 30 : 50
      } else if (totalFiles > 1000) {
        // 中等批量：标准批次大小
        return avgFileSize > 1024 * 1024 ? 40 : 60
      } else {
        // 小批量：较大批次，提高效率
        return 50
      }
    },

    // 计算批次间延迟
    calculateBatchDelay(totalFiles) {
      if (totalFiles > 15000) {
        return 1000 // 超大批量：1秒延迟
      } else if (totalFiles > 10000) {
        return 800  // 大批量：0.8秒延迟
      } else if (totalFiles > 5000) {
        return 600  // 中等批量：0.6秒延迟
      } else {
        return 500  // 小批量：0.5秒延迟
      }
    },

    // 内存清理和优化
    performMemoryCleanup() {
      try {
        // 强制垃圾回收（如果浏览器支持）
        if (window.gc && typeof window.gc === 'function') {
          window.gc()
        }

        // 清理可能的内存泄漏
        if (this.mediaManager && typeof this.mediaManager.cleanup === 'function') {
          this.mediaManager.cleanup()
        }

        // 检查内存使用情况
        if (performance.memory) {
          const memoryInfo = performance.memory
          const usedMB = Math.round(memoryInfo.usedJSHeapSize / 1024 / 1024)
          const limitMB = Math.round(memoryInfo.jsHeapSizeLimit / 1024 / 1024)

          console.log(`内存使用: ${usedMB}MB / ${limitMB}MB`)

          // 如果内存使用超过80%，增加延迟
          if (usedMB / limitMB > 0.8) {
            console.warn('内存使用率较高，增加批次间延迟')
            return true // 返回true表示需要额外延迟
          }
        }
      } catch (error) {
        console.warn('内存清理失败:', error)
      }
      return false
    },

    // 带验证的批次上传
    async uploadBatchWithValidation(files, batchIndex, totalBatches) {
      // 预验证文件
      const validFiles = files.filter(file => {
        const isValid = this.validateFileForUpload(file)
        if (!isValid) {
          console.warn(`跳过无效文件: ${file.name}`)
        }
        return isValid
      })

      if (validFiles.length === 0) {
        throw new Error('批次中没有有效文件')
      }

      const formData = new FormData()

      // 添加类别路径
      formData.append('category_path', this.currentCategory.id)

      // 添加文件
      for (let i = 0; i < validFiles.length; i++) {
        formData.append('files', validFiles[i])
      }

      // 设置超时时间，基于文件数量动态调整
      const timeoutMs = Math.max(30000, validFiles.length * 1000) // 最少30秒，每个文件额外1秒

      const response = await this.uploadWithTimeout(formData, timeoutMs)

      if (response && response.status === 'success') {
        const { image_count = 0, annotation_count = 0, total_count = 0 } = response.data

        // 累计成功统计
        this.uploadProgress.results.successCount += total_count
        this.uploadProgress.results.imageCount += image_count
        this.uploadProgress.results.annotationCount += annotation_count

        console.log(`批次${batchIndex + 1}上传成功: ${image_count}图片, ${annotation_count}标注, 总计${total_count}个文件`)

        // 验证上传结果
        if (total_count !== validFiles.length) {
          console.warn(`批次${batchIndex + 1}部分文件可能上传失败: 预期${validFiles.length}个，实际${total_count}个`)
        }
      } else {
        throw new Error(response?.message || `批次${batchIndex + 1}上传失败`)
      }
    },

    // 更新上传性能指标
    updateUploadMetrics() {
      const elapsedTime = Date.now() - this.uploadProgress.startTime
      const elapsedSeconds = elapsedTime / 1000

      // 计算上传速度 (文件/秒)
      this.uploadProgress.uploadSpeed = this.uploadProgress.current / elapsedSeconds

      // 预估剩余时间
      const remainingFiles = this.uploadProgress.total - this.uploadProgress.current
      if (this.uploadProgress.uploadSpeed > 0) {
        const remainingSeconds = remainingFiles / this.uploadProgress.uploadSpeed
        this.uploadProgress.estimatedTimeRemaining = this.formatTime(remainingSeconds)
      }
    },

    // 格式化时间显示
    formatTime(seconds) {
      if (seconds < 60) {
        return `${Math.round(seconds)}秒`
      } else if (seconds < 3600) {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = Math.round(seconds % 60)
        return `${minutes}分${remainingSeconds}秒`
      } else {
        const hours = Math.floor(seconds / 3600)
        const minutes = Math.floor((seconds % 3600) / 60)
        return `${hours}小时${minutes}分钟`
      }
    },

    // 文件上传前验证
    validateFileForUpload(file) {
      // 检查文件大小 (最大100MB)
      const MAX_FILE_SIZE = 100 * 1024 * 1024
      if (file.size > MAX_FILE_SIZE) {
        console.warn(`文件过大: ${file.name} (${this.formatFileSize(file.size)})`)
        return false
      }

      // 检查文件类型
      const allowedTypes = [
        'image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/bmp', 'image/webp',
        'text/plain', 'application/octet-stream' // txt文件
      ]

      const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.txt']
      const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))

      if (!allowedTypes.includes(file.type) && !allowedExtensions.includes(fileExtension)) {
        console.warn(`不支持的文件类型: ${file.name} (${file.type})`)
        return false
      }

      return true
    },

    // 格式化文件大小
    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    // 带超时的上传方法
    async uploadWithTimeout(formData, timeoutMs) {
      return new Promise((resolve, reject) => {
        const timeoutId = setTimeout(() => {
          reject(new Error(`上传超时 (${timeoutMs / 1000}秒)`))
        }, timeoutMs)

        APIAddSamples(formData)
          .then(response => {
            clearTimeout(timeoutId)
            resolve(response)
          })
          .catch(error => {
            clearTimeout(timeoutId)
            reject(error)
          })
      })
    },

    // 上传单个批次 (保留兼容性)
    async uploadBatch(files, batchIndex, totalBatches) {
      const formData = new FormData()

      // 添加类别路径
      formData.append('category_path', this.currentCategory.id)

      // 添加文件
      for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i])
      }

      // 使用统一的API接口
      const response = await APIAddSamples(formData)

      if (response && response.status === 'success') {
        const { image_count = 0, annotation_count = 0, total_count = 0 } = response.data

        // 累计成功统计
        this.uploadProgress.results.successCount += total_count
        this.uploadProgress.results.imageCount += image_count
        this.uploadProgress.results.annotationCount += annotation_count

        console.log(`批次${batchIndex + 1}上传成功: ${image_count}图片, ${annotation_count}标注`)
      } else {
        throw new Error(response?.message || `批次${batchIndex + 1}上传失败`)
      }
    },

    // 处理上传完成
    async handleUploadComplete() {
      const { successCount, failedCount, imageCount, annotationCount, totalFiles } = this.uploadProgress.results

      // 检查是否被取消
      if (this.uploadProgress.cancelled) {
        this.uploadProgress.status = 'cancelled'
        this.uploadProgress.message = `上传已取消！已成功上传: ${successCount}/${totalFiles} 个文件`

        if (successCount > 0) {
          ElMessage.warning(`上传已取消，但已成功上传 ${successCount} 个文件`)
          // 即使取消也要刷新列表，显示已上传的文件
          await this.loadSamplesByCategory(this.currentCategory)
        } else {
          ElMessage.info('上传已取消，没有文件被上传')
        }
        return
      }

      this.uploadProgress.status = 'completed'
      this.uploadProgress.percentage = 100

      if (successCount > 0) {
        this.uploadProgress.message = `上传完成！成功: ${successCount}/${totalFiles} (${imageCount}图片, ${annotationCount}标注)`

        if (failedCount > 0) {
          ElMessage.warning(`部分文件上传失败: 成功${successCount}个，失败${failedCount}个`)
        } else {
          ElMessage.success(`全部文件上传成功: ${imageCount}个图片样本，${annotationCount}个标注文件`)
        }

        // 重新加载当前类别的样本列表
        await this.loadSamplesByCategory(this.currentCategory)
      } else {
        this.uploadProgress.message = '没有文件上传成功'
        ElMessage.warning('没有有效的文件被上传')
      }
    },
    async deleteSamples() {
      if (this.selectedSamples.length === 0) {
        ElMessage.warning('请选择要删除的样本')
        return
      }

      try {
        await ElMessageBox.confirm(
          `确定要删除选中的 ${this.selectedSamples.length} 个样本吗？`,
          '批量删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        this.loading.operations = true
        const sampleIds = this.selectedSamples.map(sample => sample.id)

        const response = await APIDelSamplesBatch({ ids: sampleIds })
        if (response && response.status === 'success') {
          ElMessage.success(`成功删除 ${this.selectedSamples.length} 个样本`)

          // 前端实时更新：从当前列表中移除已删除的样本
          const deletedIds = new Set(sampleIds)
          this.sampleList = this.sampleList.filter(sample => !deletedIds.has(sample.id))
          this.totalSamples = Math.max(0, this.totalSamples - this.selectedSamples.length)

          // 根据当前状态选择更新策略
          if (this.isInSearchMode) {
            // 搜索状态下：可选择重新搜索以获取准确的总数
            // 这里选择保持当前列表，因为已经实时移除了删除的项目
            console.log('搜索状态下删除完成，已实时更新列表')
          } else if (this.currentCategory) {
            // 类别浏览状态下：重新加载当前类别
            await this.loadSamplesByCategory(this.currentCategory)
          }

          // 清空选择
          this.selectedSamples = []
        } else {
          throw new Error(response?.message || '批量删除样本失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.handleError(error, '批量删除样本失败')
        }
      } finally {
        this.loading.operations = false
      }
    },

    async deleteSample(row) {
      try {
        await ElMessageBox.confirm(
          `确定要删除样本"${row.name}"吗？`,
          '删除确认',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        this.loading.operations = true
        const response = await APIDelSamples({ id: row.id })
        if (response && response.status === 'success') {
          ElMessage.success(`样本"${row.name}"删除成功`)

          // 前端实时更新：从当前列表中移除已删除的样本
          this.sampleList = this.sampleList.filter(sample => sample.id !== row.id)
          this.totalSamples = Math.max(0, this.totalSamples - 1)

          // 根据当前状态选择更新策略
          if (this.isInSearchMode) {
            // 搜索状态下：已实时更新，无需重新搜索
            console.log('搜索状态下单个删除完成，已实时更新列表')
          } else if (this.currentCategory) {
            // 类别浏览状态下：重新加载当前类别
            await this.loadSamplesByCategory(this.currentCategory)
          }
        } else {
          throw new Error(response?.message || '删除样本失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.handleError(error, '删除样本失败')
        }
      } finally {
        this.loading.operations = false
      }
    },
    async viewSample(row) {
      this.loading.sampleDetail = true
      try {
        const response = await APIViewSample({ id: row.id })
        if (response && response.status === 'success') {
          // 设置图片URL
          this.currentImage = response.data?.image_url
          if (!this.currentImage) {
            throw new Error('未找到样本图片')
          }

          // 存储当前样本信息
          this.currentSample = response.data.sample

          // 解析YOLO格式标注
          if (response.data?.annotation_content) {
            this.currentAnnotations = this.parseYOLOAnnotations(response.data.annotation_content)
          } else {
            this.currentAnnotations = []
          }

          // 重置标注显示状态
          this.showAnnotations = false

        } else {
          throw new Error(response?.message || '获取样本详情失败')
        }
      } catch (error) {
        this.handleError(error, '查看样本失败')
        // 清空数据
        this.currentImage = ''
        this.currentSample = null
        this.currentAnnotations = []
      } finally {
        this.loading.sampleDetail = false
      }
    },

    async viewAnnotation(row) {
      this.loading.sampleDetail = true
      try {
        const response = await APIGetTXTContentByName({ id: row.id })
        if (response && response.status === 'success') {
          // 处理标注数据
          const annotationData = response.data
          this.displayAnnotations(annotationData)
          ElMessage.success('标注详情加载成功')
        } else {
          throw new Error(response?.message || '获取标注详情失败')
        }
      } catch (error) {
        this.handleError(error, '获取标注详情失败')
      } finally {
        this.loading.sampleDetail = false
      }
    },

    // 显示标注信息
    displayAnnotations(annotationData) {
      this.currentAnnotationData = annotationData

      // 解析YOLO格式标注
      if (annotationData.annotation_content) {
        this.parsedAnnotations = this.parseAnnotationContent(annotationData.annotation_content)
      } else {
        this.parsedAnnotations = []
      }

      // 打开标注详情对话框
      this.annotationDialogVisible = true
    },

    // 解析标注内容为结构化数据
    parseAnnotationContent(content) {
      if (!content) return []

      const lines = content.trim().split('\n')
      const annotations = []

      for (let i = 0; i < lines.length; i++) {
        const line = lines[i].trim()
        if (line) {
          const parts = line.split(/\s+/)
          if (parts.length >= 5) {
            const [classId, xCenter, yCenter, width, height] = parts.map(Number)
            annotations.push({
              lineNumber: i + 1,
              classId,
              xCenter: xCenter.toFixed(6),
              yCenter: yCenter.toFixed(6),
              width: width.toFixed(6),
              height: height.toFixed(6),
              rawLine: line
            })
          }
        }
      }

      return annotations
    },

    // 复制标注内容
    async copyAnnotationContent() {
      if (!this.currentAnnotationData?.annotation_content) {
        ElMessage.warning('没有内容可复制')
        return
      }

      try {
        await navigator.clipboard.writeText(this.currentAnnotationData.annotation_content)
        ElMessage.success('内容已复制到剪贴板')
      } catch (error) {
        // 降级方案
        const textArea = document.createElement('textarea')
        textArea.value = this.currentAnnotationData.annotation_content
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        ElMessage.success('内容已复制到剪贴板')
      }
    },

    // 在canvas上绘制标注
    drawAnnotations() {
      const canvas = this.$refs.annotationCanvas
      if (!canvas || !this.currentAnnotations.length) return

      const ctx = canvas.getContext('2d')
      const imageContainer = canvas.parentElement
      const imageElement = imageContainer.querySelector('.el-image img')

      if (!imageElement) return

      // 设置canvas尺寸与图片容器一致
      canvas.width = imageContainer.clientWidth
      canvas.height = imageContainer.clientHeight

      // 清空canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 获取图片的实际显示尺寸和位置
      const containerRect = imageContainer.getBoundingClientRect()

      const imageDisplayWidth = imageElement.clientWidth
      const imageDisplayHeight = imageElement.clientHeight
      const imageOffsetX = (containerRect.width - imageDisplayWidth) / 2
      const imageOffsetY = (containerRect.height - imageDisplayHeight) / 2

      // 绘制YOLO格式标注框
      this.currentAnnotations.forEach(annotation => {
        const { xCenter, yCenter, width, height, color, classId } = annotation

        // 将YOLO相对坐标转换为像素坐标
        const boxWidth = width * imageDisplayWidth
        const boxHeight = height * imageDisplayHeight
        const boxX = (xCenter * imageDisplayWidth) - (boxWidth / 2) + imageOffsetX
        const boxY = (yCenter * imageDisplayHeight) - (boxHeight / 2) + imageOffsetY

        // 绘制边界框
        ctx.strokeStyle = color
        ctx.lineWidth = 2
        ctx.strokeRect(boxX, boxY, boxWidth, boxHeight)

        // 绘制类别标签
        ctx.fillStyle = color
        ctx.font = '12px Arial'
        const label = `Class ${classId}`
        const labelWidth = ctx.measureText(label).width

        // 标签背景
        ctx.fillRect(boxX, boxY - 20, labelWidth + 8, 16)

        // 标签文字
        ctx.fillStyle = '#FFFFFF'
        ctx.fillText(label, boxX + 4, boxY - 8)
      })
    },
    async renameSample(row) {
      try {
        const { value } = await ElMessageBox.prompt('请输入新的样本名称', '重命名样本', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: row.name,
          inputValidator: (value) => {
            if (!value || !value.trim()) {
              return '样本名称不能为空'
            }
            if (value.trim() === row.name) {
              return '新名称不能与原名称相同'
            }
            // 检查是否与其他样本名称重复
            const exists = this.sampleList.some(sample =>
              sample.id !== row.id && sample.name === value.trim()
            )
            if (exists) {
              return '样本名称已存在，请使用其他名称'
            }
            return true
          },
          inputErrorMessage: '请输入有效的样本名称'
        })

        const newName = value.trim()
        const oldName = row.name

        this.loading.operations = true
        const response = await APIRenameSample({
          id: row.id,
          new_name: newName
        })

        if (response && response.status === 'success') {
          // 更新本地数据
          row.name = newName
          ElMessage.success(`样本 "${oldName}" 已重命名为 "${newName}"`)
        } else {
          throw new Error(response?.message || '重命名样本失败')
        }
      } catch (error) {
        if (error !== 'cancel') {
          this.handleError(error, '重命名样本失败')
        }
      } finally {
        this.loading.operations = false
      }
    },
    handleSelectionChange(selection) {
      this.selectedSamples = selection
    },
    // 解析YOLO格式标注
    parseYOLOAnnotations(annotationContent) {
      if (!annotationContent) return []

      const lines = annotationContent.trim().split('\n')
      const annotations = []

      for (const line of lines) {
        const parts = line.trim().split(/\s+/)
        if (parts.length >= 5) {
          const [classId, xCenter, yCenter, width, height] = parts.map(Number)
          annotations.push({
            classId,
            xCenter,
            yCenter,
            width,
            height,
            color: this.getClassColor(classId)
          })
        }
      }

      return annotations
    },

    // 获取类别颜色
    getClassColor(classId) {
      return this.categoryColors[classId % this.categoryColors.length]
    },

    // 提取所有子类别（叶子节点）
    extractLeafCategories(treeData, result = []) {
      treeData.forEach(node => {
        if (!node.children || node.children.length === 0) {
          // 叶子节点
          result.push({
            id: node.id,
            label: node.label,
            level: node.level || 0
          })
        } else {
          // 递归处理子节点
          this.extractLeafCategories(node.children, result)
        }
      })
      return result
    },

    // 统计每个类别的样本数量
    async getCategorySampleCounts(categories) {
      const counts = {}

      for (const category of categories) {
        try {
          const response = await APIGetSamplesListByName({
            category_path: category.id,
            page: 1,
            limit: 1  // 只需要获取总数，不需要具体数据
          })

          if (response && response.status === 'success') {
            counts[category.id] = response.data?.total || 0
          } else {
            counts[category.id] = 0
          }
        } catch (error) {
          console.warn(`获取类别 ${category.label} 的样本数量失败:`, error)
          counts[category.id] = 0
        }
      }

      return counts
    },

    // 生成类别列表数据
    async generateCategoryList() {
      if (!this.treeData || this.treeData.length === 0) {
        this.categoryList = []
        return
      }

      // 提取所有子类别
      const leafCategories = this.extractLeafCategories(this.treeData)

      // 获取样本数量统计
      const sampleCounts = await this.getCategorySampleCounts(leafCategories)

      // 生成类别列表
      this.categoryList = leafCategories.map((category, index) => ({
        index: index,
        id: category.id,
        name: category.label,
        color: this.categoryColors[index % this.categoryColors.length],
        count: sampleCounts[category.id] || 0
      }))
    },

    toggleAnnotations() {
      this.showAnnotations = !this.showAnnotations
      if (this.showAnnotations && this.currentAnnotations.length > 0) {
        this.$nextTick(() => {
          this.drawAnnotations()
        })
      }
    },

    // 图片缩放功能
    handleImageWheel(event) {
      event.preventDefault()
      const delta = event.deltaY > 0 ? -0.1 : 0.1
      this.imageScale = Math.max(0.1, Math.min(5, this.imageScale + delta))
      this.updateImageTransform()
    },

    // 图片拖动开始
    handleImageMouseDown(event) {
      if (this.imageScale > 1) {
        this.isDragging = true
        this.dragStart = {
          x: event.clientX - this.imagePosition.x,
          y: event.clientY - this.imagePosition.y
        }
      }
    },

    // 图片拖动中
    handleImageMouseMove(event) {
      if (this.isDragging) {
        this.imagePosition = {
          x: event.clientX - this.dragStart.x,
          y: event.clientY - this.dragStart.y
        }
        this.updateImageTransform()
      }
    },

    // 图片拖动结束
    handleImageMouseUp() {
      this.isDragging = false
    },

    // 更新图片变换
    updateImageTransform() {
      const imageElement = this.$el.querySelector('.el-image img')
      if (imageElement) {
        imageElement.style.transform = `scale(${this.imageScale}) translate(${this.imagePosition.x / this.imageScale}px, ${this.imagePosition.y / this.imageScale}px)`
        imageElement.style.cursor = this.imageScale > 1 ? 'grab' : 'default'
      }

      // 重新绘制标注
      if (this.showAnnotations) {
        this.$nextTick(() => {
          this.drawAnnotations()
        })
      }
    },

    // 重置图片缩放和位置（预览模式已移除此功能）
    resetImageView() {
      this.imageScale = 1
      this.imagePosition = { x: 0, y: 0 }
      this.updateImageTransform()
    },

    // 打开图像查看器
    openImageViewer() {
      if (!this.currentImage) return
      this.imageViewerVisible = true
      this.viewerShowAnnotations = false
      this.resetViewerView()
    },

    // 关闭图像查看器
    closeImageViewer() {
      this.imageViewerVisible = false
      this.viewerShowAnnotations = false
      this.resetViewerView()
    },

    // 查看器标注切换
    toggleViewerAnnotations() {
      this.viewerShowAnnotations = !this.viewerShowAnnotations
      if (this.viewerShowAnnotations && this.currentAnnotations.length > 0) {
        this.$nextTick(() => {
          this.drawViewerAnnotations()
        })
      }
    },

    // 防抖的标注重绘方法
    debouncedRedrawAnnotations() {
      if (this.annotationRedrawTimer) {
        clearTimeout(this.annotationRedrawTimer)
      }

      this.annotationRedrawTimer = setTimeout(() => {
        if (this.viewerShowAnnotations && this.currentAnnotations.length > 0) {
          this.drawViewerAnnotations()
        }
      }, 16) // 约60fps的重绘频率
    },

    // 重置查看器视图
    resetViewerView() {
      this.viewerImageScale = 1
      this.viewerImagePosition = { x: 0, y: 0 }
      this.viewerIsDragging = false
      // 使用防抖重绘
      this.debouncedRedrawAnnotations()
    },

    // 查看器滚轮缩放
    handleViewerWheel(event) {
      event.preventDefault()
      const delta = event.deltaY > 0 ? -0.05 : 0.05 // 更细腻的缩放步长
      const newScale = Math.max(0.1, Math.min(5, this.viewerImageScale + delta))

      if (newScale !== this.viewerImageScale) {
        this.viewerImageScale = newScale
        // 使用防抖重绘，提高滚轮缩放流畅性
        this.debouncedRedrawAnnotations()
      }
    },

    // 查看器拖动开始
    handleViewerMouseDown(event) {
      if (this.viewerImageScale > 1) {
        this.viewerIsDragging = true
        this.viewerDragStart = {
          x: event.clientX - this.viewerImagePosition.x,
          y: event.clientY - this.viewerImagePosition.y
        }
      }
    },

    // 查看器拖动中
    handleViewerMouseMove(event) {
      if (this.viewerIsDragging) {
        this.viewerImagePosition = {
          x: event.clientX - this.viewerDragStart.x,
          y: event.clientY - this.viewerDragStart.y
        }
        // 使用防抖重绘，提高拖动流畅性
        this.debouncedRedrawAnnotations()
      }
    },

    // 查看器拖动结束
    handleViewerMouseUp() {
      this.viewerIsDragging = false
    },

    // 绘制查看器标注
    drawViewerAnnotations() {
      const canvas = this.$refs.viewerAnnotationCanvas
      if (!canvas || !this.currentAnnotations.length) return

      const ctx = canvas.getContext('2d')
      const imageElement = this.$refs.viewerImage

      if (!imageElement) return

      // 设置canvas尺寸与图片容器一致
      const container = canvas.parentElement
      canvas.width = container.clientWidth
      canvas.height = container.clientHeight

      // 清空canvas
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 获取图片的实际显示尺寸和位置
      const containerRect = container.getBoundingClientRect()
      const imageRect = imageElement.getBoundingClientRect()

      const imageDisplayWidth = imageRect.width / this.viewerImageScale
      const imageDisplayHeight = imageRect.height / this.viewerImageScale
      const imageOffsetX = (containerRect.width - imageRect.width) / 2
      const imageOffsetY = (containerRect.height - imageRect.height) / 2

      // 绘制YOLO格式标注框
      this.currentAnnotations.forEach(annotation => {
        const { xCenter, yCenter, width, height, color, classId } = annotation

        // 将YOLO相对坐标转换为像素坐标
        const boxWidth = width * imageDisplayWidth * this.viewerImageScale
        const boxHeight = height * imageDisplayHeight * this.viewerImageScale
        const boxX = (xCenter * imageDisplayWidth * this.viewerImageScale) - (boxWidth / 2) + imageOffsetX + this.viewerImagePosition.x
        const boxY = (yCenter * imageDisplayHeight * this.viewerImageScale) - (boxHeight / 2) + imageOffsetY + this.viewerImagePosition.y

        // 绘制边界框
        ctx.strokeStyle = color
        ctx.lineWidth = 2
        ctx.strokeRect(boxX, boxY, boxWidth, boxHeight)

        // 绘制类别标签
        ctx.fillStyle = color
        ctx.font = '12px Arial'
        const label = `Class ${classId}`
        const labelWidth = ctx.measureText(label).width

        // 标签背景
        ctx.fillRect(boxX, boxY - 20, labelWidth + 8, 16)

        // 标签文字
        ctx.fillStyle = '#FFFFFF'
        ctx.fillText(label, boxX + 4, boxY - 8)
      })
    },
    goBack() {
      this.$router.go(-1)
    },

    // 类别编辑相关方法
    openCategoryDialog() {
      this.categoryDialogVisible = true
      this.editableTreeData = this.deepClone(this.treeData)
      this.hasChanges = false
    },

    handleDialogClose(done) {
      if (this.hasChanges) {
        ElMessageBox.confirm(
          '您有未保存的更改，确定要关闭吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        ).then(() => {
          done()
        }).catch(() => {
          // 取消关闭
        })
      } else {
        done()
      }
    },

    addCategory() {
      if (!this.newCategory.name.trim()) {
        ElMessage.warning('请输入类别名称')
        return
      }

      const categoryName = this.newCategory.name.trim()

      // 检查类别名称是否已存在
      if (this.isCategoryNameExists(categoryName)) {
        ElMessage.warning('类别名称已存在，请使用其他名称')
        return
      }

      const newNode = {
        id: this.nextCategoryId++,
        label: categoryName,
        children: []
      }

      if (this.newCategory.parentId) {
        const parent = this.findNodeById(this.editableTreeData, this.newCategory.parentId)
        if (parent) {
          if (!parent.children) {
            parent.children = []
          }
          parent.children.push(newNode)
        }
      } else {
        this.editableTreeData.push(newNode)
      }

      this.newCategory.name = ''
      this.newCategory.parentId = null
      this.hasChanges = true
      ElMessage.success('类别添加成功')
    },

    startEdit(data) {
      data.editing = true
      data.tempLabel = data.label
      this.$nextTick(() => {
        const inputs = this.$refs.editInput
        if (inputs && inputs.length > 0) {
          inputs[inputs.length - 1].focus()
        }
      })
    },

    saveEdit(data) {
      if (!data.tempLabel || !data.tempLabel.trim()) {
        ElMessage.warning('类别名称不能为空')
        return
      }

      const newLabel = data.tempLabel.trim()

      // 检查新名称是否与其他类别重复（排除自身）
      if (newLabel !== data.label && this.isCategoryNameExists(newLabel)) {
        ElMessage.warning('类别名称已存在，请使用其他名称')
        return
      }

      data.label = newLabel
      data.editing = false
      delete data.tempLabel
      this.hasChanges = true
      ElMessage.success('类别修改成功')
    },

    cancelEdit(data) {
      data.editing = false
      delete data.tempLabel
    },

    deleteCategory(data) {
      ElMessageBox.confirm(
        `确定要删除类别"${data.label}"吗？${data.children && data.children.length > 0 ? '删除后其子类别也将被删除。' : ''}`,
        '删除确认',
        {
          confirmButtonText: '确定删除',
          cancelButtonText: '取消',
          type: 'warning'
        }
      ).then(() => {
        this.removeNodeFromTree(this.editableTreeData, data.id)
        this.hasChanges = true
        ElMessage.success('类别删除成功')
      }).catch(() => {
        // 取消删除
      })
    },

    async saveCategoryChanges() {
      this.loading.operations = true
      try {
        // 转换编辑后的树数据为API需要的格式
        // const categoryData = this.convertTreeDataForAPI(this.editableTreeData)
        const categoryData = this.editableTreeData

        const response = await APISaveCategoryTree({ tree_data: categoryData })
        if (response && response.status === 'success') {
          this.treeData = this.deepClone(this.editableTreeData)
          this.categoryDialogVisible = false
          this.hasChanges = false
          ElMessage.success('类别更改已保存')
        } else {
          throw new Error(response?.message || '保存类别更改失败')
        }
      } catch (error) {
        this.handleError(error, '保存类别更改失败')
      } finally {
        this.loading.operations = false
      }
    },

    // 转换树数据为API格式
    convertTreeDataForAPI(treeData) {
      return treeData.map(item => ({
        id: item.id,
        name: item.label,
        children: item.children ? this.convertTreeDataForAPI(item.children) : []
      }))
    },

    cancelCategoryEdit() {
      if (this.hasChanges) {
        ElMessageBox.confirm(
          '您有未保存的更改，确定要取消吗？',
          '提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '继续编辑',
            type: 'warning'
          }
        ).then(() => {
          this.categoryDialogVisible = false
          this.hasChanges = false
        })
      } else {
        this.categoryDialogVisible = false
      }
    },

    // 工具方法
    deepClone(obj) {
      return JSON.parse(JSON.stringify(obj))
    },

    findNodeById(tree, id) {
      for (let node of tree) {
        if (node.id === id) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = this.findNodeById(node.children, id)
          if (found) return found
        }
      }
      return null
    },

    removeNodeFromTree(tree, id) {
      for (let i = 0; i < tree.length; i++) {
        if (tree[i].id === id) {
          tree.splice(i, 1)
          return true
        }
        if (tree[i].children && tree[i].children.length > 0) {
          if (this.removeNodeFromTree(tree[i].children, id)) {
            return true
          }
        }
      }
      return false
    },

    // 检查类别名称是否已存在的辅助方法
    isCategoryNameExists(name) {
      const checkNode = (nodes) => {
        for (let node of nodes) {
          if (node.label === name) {
            return true
          }
          if (node.children && node.children.length > 0) {
            if (checkNode(node.children)) {
              return true
            }
          }
        }
        return false
      }
      return checkNode(this.editableTreeData)
    }
  }
}
</script>

<style scoped>
@import '@/assets/css/variables.css';

.sample {
  overflow: hidden;
  background-size: cover;
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  background-image: url("../assets/imgs/backimg.png");
}

.content {
  display: flex;
  gap: 15px;
  /* 减少间距，为中间区域提供更多空间 */
  padding: 15px;
  /* 减少内边距 */
  height: calc(100vh - 180px);
  box-sizing: border-box;
}

/* 统一面板样式 */
.panel {
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 2px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.left {
  width: 300px;
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 2px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.search-section {
  margin-bottom: 20px;
}

.category-tree {
  flex: 1;
  overflow-y: auto;
}

.tree-node {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
}

.center {
  flex: 2;
  /* 增加flex比例，让中间区域占更多空间 */
  min-width: 600px;
  /* 设置最小宽度确保有足够空间 */
  max-width: calc(100vw - 760px);
  /* 适当增加最大宽度，左侧300px + 右侧400px + 间距60px */
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 2px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
  overflow: hidden;
  /* 防止内容溢出 */
}

.function-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 15px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.function-left {
  display: flex;
  align-items: center;
}

.function-right {
  display: flex;
  gap: 10px;
}

.sample-list {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  /* 防止flex子项溢出 */
  overflow: hidden;
  /* 防止内容溢出 */
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
  padding: 15px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

/* 移除固定定位的分页器样式 */
/* .fixed-pagination 样式已删除 */

/* 响应式设计 */
@media (max-width: 1200px) {
  .content {
    flex-direction: column;
    height: auto;
  }

  .left,
  .right,
  .center {
    width: 100%;
    min-width: auto;
    /* 移除最小宽度限制 */
    max-width: none;
    /* 移除最大宽度限制 */
  }

  .function-bar {
    flex-direction: column;
    gap: 15px;
  }

  .function-left,
  .function-right {
    width: 100%;
    justify-content: center;
  }

  .pagination-container {
    margin-top: 15px;
    padding: 10px;
  }
}

.right {
  width: 400px;
  /* 减少右侧宽度，为中间区域让出空间 */
  min-width: 350px;
  /* 设置最小宽度确保功能正常 */
  background-color: var(--bg-primary);
  border-radius: var(--radius-lg);
  padding: 20px;
  border: 2px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  height: calc(100vh - 180px);
}

.image-display {
  margin-bottom: 0;
}

.image-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.image-title {
  color: var(--text-primary);
  font-size: 16px;
  font-weight: bold;
}

.image-container {
  position: relative;
  height: 250px;
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-md);
  overflow: hidden;
  background-color: var(--bg-tertiary);
  margin-bottom: 15px;
}

.no-image {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: var(--text-muted);
}

.annotation-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

/* 图像查看器样式 - 主题化设计 */
.image-viewer-dialog {
  .el-dialog {
    border-radius: var(--radius-lg);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    padding: 16px 24px;

    .el-dialog__title {
      color: white;
      font-weight: 600;
      font-size: 18px;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: white;
      font-size: 18px;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .el-dialog__body {
    padding: 0;
    background: var(--bg-primary);
  }
}

.viewer-header {
  padding: 16px 24px;
  border-bottom: 1px solid var(--border-secondary);
  background: var(--bg-secondary);
}

.viewer-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.zoom-info {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--text-primary);
  font-weight: 600;
  font-size: 16px;
  background: var(--bg-primary);
  padding: 8px 16px;
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.viewer-container {
  position: relative;
  height: 75vh;
  overflow: hidden;
  background: var(--bg-primary);
  background-image:
    linear-gradient(45deg, var(--bg-secondary) 25%, transparent 25%),
    linear-gradient(-45deg, var(--bg-secondary) 25%, transparent 25%),
    linear-gradient(45deg, transparent 75%, var(--bg-secondary) 75%),
    linear-gradient(-45deg, transparent 75%, var(--bg-secondary) 75%);
  background-size: 20px 20px;
  background-position: 0 0, 0 10px, 10px -10px, -10px 0px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-top: 1px solid var(--border-secondary);
}

.viewer-image {
  max-width: 100%;
  max-height: 100%;
  user-select: none;
  transform-origin: center;
  border-radius: var(--radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.viewer-annotation-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 10;
}

/* 标注详情对话框样式 - 重新设计高对比度版本 */
.annotation-dialog {
  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #ffffff;
    border-radius: 8px 8px 0 0;

    .el-dialog__title {
      color: #ffffff;
      font-weight: 600;
      font-size: 18px;
    }

    .el-dialog__headerbtn .el-dialog__close {
      color: #ffffff;
      font-size: 20px;

      &:hover {
        color: #f0f0f0;
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: #fafbfc;
  }

  .el-dialog {
    border-radius: 12px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  }
}

.annotation-content {
  .annotation-info {
    margin-bottom: 24px;
    padding: 20px;
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border: 2px solid #2196f3;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(33, 150, 243, 0.15);

    h4 {
      margin: 0 0 16px 0;
      color: #1565c0;
      font-size: 18px;
      font-weight: 700;
      display: flex;
      align-items: center;

      &::before {
        content: "📋";
        margin-right: 8px;
        font-size: 20px;
      }
    }

    p {
      margin: 12px 0;
      color: #37474f;
      font-size: 15px;
      line-height: 1.6;

      strong {
        color: #0d47a1;
        font-weight: 600;
      }
    }
  }

  .annotation-raw {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      color: #2e7d32;
      font-size: 18px;
      font-weight: 700;
      display: flex;
      align-items: center;

      &::before {
        content: "📄";
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .el-textarea {
      .el-textarea__inner {
        background: #f8f9fa;
        border: 2px solid #4caf50;
        border-radius: 8px;
        color: #263238;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;

        &:focus {
          border-color: #2e7d32;
          box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
        }
      }
    }

    .raw-actions {
      margin-top: 12px;
      text-align: right;

      .el-button {
        background: linear-gradient(135deg, #4caf50 0%, #45a049 100%);
        border: none;
        color: #ffffff;
        font-weight: 600;
        border-radius: 6px;
        padding: 8px 16px;

        &:hover {
          background: linear-gradient(135deg, #45a049 0%, #3d8b40 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
        }
      }
    }
  }

  .annotation-table {
    h4 {
      margin: 0 0 16px 0;
      color: #e65100;
      font-size: 18px;
      font-weight: 700;
      display: flex;
      align-items: center;

      &::before {
        content: "📊";
        margin-right: 8px;
        font-size: 20px;
      }
    }

    .el-table {
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);

      .el-table__header {
        background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);

        th {
          background: transparent !important;
          color: #ffffff !important;
          font-weight: 600;
          font-size: 14px;
          border-bottom: none;
        }
      }

      .el-table__body {
        tr {
          &:nth-child(even) {
            background: #fff3e0;
          }

          &:hover {
            background: #ffe0b2 !important;
          }

          td {
            color: #424242;
            font-weight: 500;
            border-bottom: 1px solid #e0e0e0;

            &:first-child {
              color: #d32f2f;
              font-weight: 600;
            }

            &:nth-child(2) {
              color: #1976d2;
              font-weight: 600;
            }
          }
        }
      }
    }
  }

  .no-annotation {
    text-align: center;
    padding: 60px 0;
    background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
    border-radius: 12px;
    border: 2px dashed #bdbdbd;

    .el-empty {
      .el-empty__description {
        color: #616161;
        font-size: 16px;
        font-weight: 500;
      }
    }
  }
}

.description {
  color: var(--text-primary);
  flex: 1;
  overflow-y: auto;
  overflow-x: hidden;
  padding-right: 8px;
  word-wrap: break-word;
}

.description h4 {
  margin-bottom: 15px;
  color: var(--primary-color);
  border-left: 4px solid var(--primary-color);
  padding-left: 10px;
  position: sticky;
  top: 0;
  background-color: var(--bg-primary);
  z-index: 1;
  margin-top: 0;
  padding-top: 5px;
  padding-bottom: 5px;
}

.annotation-info-container {
  width: 100%;
  max-width: 100%;
  height: 400px;
  padding: 10px;
  background-color: var(--bg-secondary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
  box-shadow: var(--shadow-primary);
  color: var(--text-primary);
  margin-top: 10px;
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  word-wrap: break-word;
  word-break: break-all;
  box-sizing: border-box;
}

.annotation-section {
  margin-bottom: 15px;
  border-bottom: 1px solid var(--border-secondary);
  padding-bottom: 8px;
}

.annotation-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.annotation-title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 8px;
  color: var(--primary-dark);
  display: flex;
  align-items: center;
}

.annotation-title::before {
  content: "";
  display: inline-block;
  width: 6px;
  height: 16px;
  background-color: var(--primary-dark);
  margin-right: 8px;
  border-radius: var(--radius-sm);
}

.annotation-content {
  margin: 5px 0 5px 15px;
  line-height: 1.5;
  color: #aaa;
  font-size: 13px;
  word-wrap: break-word;
  /* 长单词自动换行 */
  word-break: break-word;
  /* 在单词边界换行 */
  white-space: normal;
  /* 允许正常换行 */
  overflow-wrap: break-word;
  /* 现代浏览器的换行属性 */
}

.highlight-text {
  font-weight: bold;
  color: #0aa3e0;
  background-color: rgba(10, 163, 224, 0.1);
  padding: 0 4px;
  border-radius: 3px;
  word-break: break-all;
  /* 高亮文本也要换行 */
  display: inline-block;
  /* 确保换行效果 */
}

.class-grid {
  display: flex;
  flex-wrap: wrap;
  margin-left: 15px;
  width: 100%;
  box-sizing: border-box;
}

.class-item {
  width: 48%;
  margin-bottom: 6px;
  display: flex;
  align-items: center;
  padding: 3px;
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
  box-sizing: border-box;
  word-wrap: break-word;
}

.class-item:hover {
  background-color: var(--bg-tertiary);
  transform: translateX(3px);
}

.class-color {
  display: inline-block;
  width: 14px;
  height: 14px;
  margin-right: 8px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-secondary);
  box-shadow: var(--shadow-sm);
}

.class-name {
  display: inline-block;
  font-size: 13px;
  color: #fff;
  flex: 1;
  word-wrap: break-word;
  /* 类别名称自动换行 */
  word-break: break-word;
  overflow-wrap: break-word;
}

.class-count {
  display: inline-block;
  font-size: 12px;
  color: #fff;
  font-weight: bold;
  margin-left: 5px;
  background-color: rgba(64, 158, 255, 0.1);
  padding: 2px 6px;
  border-radius: 10px;
  border: 1px solid rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.class-count.zero-count {
  color: #909399;
  background-color: rgba(144, 147, 153, 0.1);
  border: 1px solid rgba(144, 147, 153, 0.3);
}

.class-item:hover .class-count {
  background: #dee2e6;
}

.class-item:hover .zero-count {
  background: #f5c6cb;
}

.no-categories {
  text-align: center;
  padding: 20px;
  color: #6c757d;
}

/* 自定义滚动条样式 - 只显示垂直滚动条 */
.annotation-info-container::-webkit-scrollbar {
  width: 6px;
  height: 0;
  /* 隐藏水平滚动条 */
}

.annotation-info-container::-webkit-scrollbar:horizontal {
  display: none;
  /* 完全隐藏水平滚动条 */
}

.annotation-info-container::-webkit-scrollbar-thumb {
  background-color: rgba(10, 163, 224, 0.5);
  border-radius: 10px;
}

.annotation-info-container::-webkit-scrollbar-track {
  background: transparent;
  border-radius: 10px;
}

/* 树形组件样式优化 */
:deep(.el-tree) {
  background: transparent;
  color: var(--text-primary);
}

:deep(.el-tree-node__content) {
  background: transparent;
  color: var(--text-primary);
  height: 32px;
  border-radius: 4px;
  margin: 2px 0;
  transition: var(--transition-normal);
}

:deep(.el-tree-node__content:hover) {
  background-color: rgba(77, 163, 171, 0.2);
  color: var(--primary-color);
}

:deep(.el-tree-node.is-current > .el-tree-node__content),
:deep(.el-tree-node__content.is-current) {
  background-color: rgba(77, 163, 171, 0.4) !important;
  color: var(--text-primary) !important;
  font-weight: bold;
}

/* 统一的表格样式 */
:deep(.el-table) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

:deep(.el-table .el-table__cell) {
  text-align: center;
  font-size: 13px;
  border-right: 1px solid rgba(77, 163, 171, 0.2);
}

:deep(.el-table th) {
  background-color: var(--bg-primary) !important;
  color: var(--primary-color) !important;
  border-bottom: 2px solid var(--border-primary);
  font-weight: bold;
  font-size: 14px;
}

:deep(.el-table td) {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-secondary);
  text-align: center;
  font-size: 13px;
}

:deep(.el-table tr:hover td) {
  background-color: rgba(77, 163, 171, 0.2) !important;
}

/* 优化的按钮样式 - 合并重复属性 */
:deep(.el-button) {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 主题色按钮 */
:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
}

/* 简化的滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

/* 应用滚动条主题 */
.description,
:deep(.el-table__body-wrapper) {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--bg-primary);
}

.description::-webkit-scrollbar,
:deep(.el-table__body-wrapper::-webkit-scrollbar) {
  width: 8px;
  height: 8px;
}

.description::-webkit-scrollbar-track,
:deep(.el-table__body-wrapper::-webkit-scrollbar-track) {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.description::-webkit-scrollbar-thumb,
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-sm);
}

.description::-webkit-scrollbar-thumb:hover,
:deep(.el-table__body-wrapper::-webkit-scrollbar-thumb:hover) {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

/* 表格选择框样式优化 */
:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #4dd3ff;
  border-color: #4dd3ff;
  box-shadow: 0 0 5px rgba(77, 211, 255, 0.5);
}

:deep(.el-checkbox__inner) {
  border-color: rgba(77, 163, 171, 0.6);
  border-radius: 3px;
}

:deep(.el-checkbox__inner:hover) {
  border-color: #4dd3ff;
}

/* 分页器样式优化 */
:deep(.el-pagination) {
  color: var(--text-primary);
}

:deep(.el-pagination .el-pager li) {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  margin: 0 3px;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  min-width: 32px;
  height: 32px;
  line-height: 30px;
}

:deep(.el-pagination .el-pager li:hover) {
  background: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: var(--primary-color);
  color: var(--text-primary);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  height: 32px;
  min-width: 32px;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  background: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

:deep(.el-pagination .el-select .el-select__wrapper) {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  height: 32px;
}

:deep(.el-pagination .el-select .el-select__placeholder) {
  color: var(--text-primary);
}

/* 输入框样式优化 */
:deep(.el-input__wrapper) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
}

:deep(.el-input__inner) {
  color: var(--text-primary);
}

:deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 12px var(--shadow-primary);
}

:deep(.el-input__prefix) {
  color: rgba(255, 255, 255, 0.6);
}

:deep(.el-input__suffix) {
  color: rgba(255, 255, 255, 0.6);
}

/* 按钮样式统一优化 */
:deep(.el-button) {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition-normal);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #529b2e 100%);
  border-color: #67c23a;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

:deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  border-color: #85ce61;
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);
  border-color: #f56c6c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
  border-color: #f78989;
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #d48806 100%);
  border-color: #e6a23c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

:deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%);
  border-color: #ebb563;
}

:deep(.el-button.is-disabled) {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-muted) !important;
  box-shadow: none !important;
  transform: none !important;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: var(--radius-sm);
}

/* 移除重复的响应式设计代码 */

/* 编辑类别按钮区域 */
.edit-category-section {
  margin-bottom: 15px;
}

/* 类别编辑对话框样式 */
.category-editor {
  max-height: 500px;
  overflow-y: auto;
}

.tree-node-editor {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
}

.node-label {
  flex: 1;
  margin: 0 8px;
}

.node-actions {
  display: flex;
  gap: 4px;
  margin-left: auto;
}

.node-actions .el-button {
  padding: 4px 8px;
  min-width: auto;
}

.add-category-section {
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid var(--border-secondary);
}

.add-form {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-top: 10px;
}

.category-list-section h4 {
  color: var(--primary-color);
  margin-bottom: 15px;
}

.category-edit-tree {
  max-height: 300px;
  overflow-y: auto;
}

.fixed-pagination {
  position: fixed;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-primary);
  padding: 10px 20px;
  border-radius: var(--radius-lg);
  border: 2px solid var(--border-primary);
  box-shadow: var(--shadow-md);
  z-index: 1000;
}

/* 统一的滚动条样式 */
.custom-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-sm);
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
}

/* 加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: var(--text-muted);
  font-size: 14px;
}

.loading-container .el-icon {
  font-size: 24px;
  margin-bottom: 10px;
  color: var(--primary-color);
}

/* 应用滚动条主题 */
.description,
.annotation-info-container,
.category-tree,
.category-editor {
  scrollbar-width: thin;
  scrollbar-color: var(--primary-color) var(--bg-primary);
}

.description::-webkit-scrollbar,
.annotation-info-container::-webkit-scrollbar,
.category-tree::-webkit-scrollbar,
.category-editor::-webkit-scrollbar {
  width: 8px;
}

.description::-webkit-scrollbar-track,
.annotation-info-container::-webkit-scrollbar-track,
.category-tree::-webkit-scrollbar-track,
.category-editor::-webkit-scrollbar-track {
  background: var(--bg-primary);
  border-radius: var(--radius-sm);
}

.description::-webkit-scrollbar-thumb,
.annotation-info-container::-webkit-scrollbar-thumb,
.category-tree::-webkit-scrollbar-thumb,
.category-editor::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-sm);
}

/* Element Plus 组件样式覆盖 */
:deep(.el-tree-node__content.is-current) {
  background-color: var(--border-primary) !important;
  color: var(--text-primary) !important;
}

:deep(.el-table) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  overflow: hidden;
}

:deep(.el-table .el-table__cell) {
  text-align: center;
  font-size: 13px;
  border-right: 1px solid var(--border-secondary);
}

:deep(.el-table th) {
  background-color: var(--bg-primary) !important;
  color: var(--primary-color) !important;
  border-bottom: 2px solid var(--border-primary);
  font-weight: bold;
  font-size: 14px;
}

:deep(.el-table td) {
  background-color: var(--bg-tertiary) !important;
  color: var(--text-primary) !important;
  border-bottom: 1px solid var(--border-secondary);
  text-align: center;
  font-size: 13px;
}

:deep(.el-table tr:hover td) {
  background-color: var(--border-primary) !important;
}

:deep(.el-button) {
  border-radius: var(--radius-md);
  font-weight: 500;
  transition: var(--transition-normal);
  box-shadow: var(--shadow-sm);
}

:deep(.el-button:hover) {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

:deep(.el-button--primary) {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  border-color: var(--primary-color);
  color: var(--text-primary);
}

:deep(.el-button--primary:hover) {
  background: linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%);
  border-color: var(--primary-light);
}

:deep(.el-button--success) {
  background: linear-gradient(135deg, #67c23a 0%, #529b2e 100%);
  border-color: #67c23a;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

:deep(.el-button--success:hover) {
  background: linear-gradient(135deg, #85ce61 0%, #67c23a 100%);
  border-color: #85ce61;
}

:deep(.el-button--danger) {
  background: linear-gradient(135deg, #f56c6c 0%, #e6a23c 100%);
  border-color: #f56c6c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

:deep(.el-button--danger:hover) {
  background: linear-gradient(135deg, #f78989 0%, #f56c6c 100%);
  border-color: #f78989;
}

:deep(.el-button--warning) {
  background: linear-gradient(135deg, #e6a23c 0%, #d48806 100%);
  border-color: #e6a23c;
  color: #ffffff;
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

:deep(.el-button--warning:hover) {
  background: linear-gradient(135deg, #ebb563 0%, #e6a23c 100%);
  border-color: #ebb563;
}

:deep(.el-button.is-disabled) {
  background: var(--bg-tertiary) !important;
  border-color: var(--border-secondary) !important;
  color: var(--text-muted) !important;
  box-shadow: none !important;
  transform: none !important;
}

:deep(.el-button--small) {
  padding: 6px 12px;
  font-size: 12px;
  border-radius: var(--radius-sm);
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
  box-shadow: 0 0 5px var(--shadow-primary);
}

:deep(.el-checkbox__inner) {
  border-color: var(--border-primary);
  border-radius: var(--radius-sm);
}

:deep(.el-checkbox__inner:hover) {
  border-color: var(--primary-color);
}

:deep(.el-pagination) {
  color: var(--text-primary);
}

:deep(.el-pagination .el-pager li) {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  margin: 0 3px;
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  min-width: 32px;
  height: 32px;
  line-height: 30px;
}

:deep(.el-pagination .el-pager li:hover) {
  background: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

:deep(.el-pagination .el-pager li.is-active) {
  background: var(--primary-color);
  color: var(--text-primary);
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  background: var(--bg-primary);
  color: var(--text-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  height: 32px;
  min-width: 32px;
}

:deep(.el-pagination .btn-prev:hover),
:deep(.el-pagination .btn-next:hover) {
  background: var(--border-primary);
  color: var(--primary-color);
  transform: translateY(-1px);
  box-shadow: var(--shadow-primary);
}

:deep(.el-pagination .el-select .el-select__wrapper) {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  height: 32px;
}

:deep(.el-pagination .el-select .el-select__placeholder) {
  color: var(--text-primary);
}

:deep(.el-input__wrapper) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  color: var(--text-primary);
  border-radius: var(--radius-md);
  transition: var(--transition-normal);
  backdrop-filter: blur(10px);
}

:deep(.el-input__inner) {
  color: var(--text-primary);
}

:deep(.el-input__wrapper:hover) {
  border-color: var(--primary-color);
  box-shadow: var(--shadow-primary);
}

:deep(.el-input__wrapper.is-focus) {
  border-color: var(--primary-color);
  box-shadow: 0 0 12px var(--shadow-primary);
}

:deep(.el-input__prefix),
:deep(.el-input__suffix) {
  color: var(--text-muted);
}

:deep(.el-dialog) {
  background: var(--bg-primary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-lg);
  backdrop-filter: blur(10px);
}

:deep(.el-dialog__header) {
  background: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

:deep(.el-dialog__title) {
  color: var(--primary-color);
  font-weight: bold;
}

:deep(.el-dialog__body) {
  color: var(--text-primary);
}

:deep(.el-dialog__footer) {
  background: var(--bg-secondary);
  border-top: 1px solid var(--border-primary);
  border-radius: 0 0 var(--radius-lg) var(--radius-lg);
}

:deep(.el-select__wrapper) {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
}

:deep(.el-select__placeholder) {
  color: var(--text-muted);
}

:deep(.el-tree) {
  background: transparent;
  color: var(--text-primary);
}

:deep(.el-tree-node__content) {
  color: var(--text-primary);
  border-radius: var(--radius-sm);
  transition: var(--transition-normal);
}

:deep(.el-tree-node__content:hover) {
  background-color: var(--bg-tertiary);
}

/* 上传进度对话框样式 */
.upload-progress-content {
  padding: 20px 0;
}

.progress-section {
  margin-bottom: 20px;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-title {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 14px;
}

.progress-stats {
  font-size: 13px;
  color: var(--text-secondary);
  font-weight: 500;
}

.status-message {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
  font-size: 14px;
  color: var(--text-primary);
}

.status-message .el-icon {
  font-size: 16px;
}

.result-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
  padding: 16px;
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: var(--radius-sm);
  background: var(--bg-tertiary);
}

.stat-item.success {
  background: rgba(103, 194, 58, 0.1);
  border: 1px solid rgba(103, 194, 58, 0.3);
}

.stat-item.error {
  background: rgba(245, 108, 108, 0.1);
  border: 1px solid rgba(245, 108, 108, 0.3);
}

.stat-label {
  font-size: 12px;
  color: var(--text-secondary);
  font-weight: 500;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: var(--text-primary);
}

.stat-item.success .stat-value {
  color: #67c23a;
}

.stat-item.error .stat-value {
  color: #f56c6c;
}

/* 性能指标样式 */
.performance-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 12px;
  padding: 12px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-secondary);
}

.metric-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: var(--radius-sm);
  background: var(--bg-secondary);
}

.metric-label {
  font-size: 11px;
  color: var(--text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.metric-value {
  font-size: 13px;
  font-weight: 600;
  color: var(--primary-color);
}

/* 失败批次详情样式 */
.failed-batches {
  margin-top: 16px;
  padding: 12px;
  background: rgba(245, 108, 108, 0.05);
  border: 1px solid rgba(245, 108, 108, 0.2);
  border-radius: var(--radius-md);
}

.failed-batches h5 {
  margin: 0 0 8px 0;
  color: #f56c6c;
  font-size: 14px;
  font-weight: 600;
}

.failed-batch-list {
  max-height: 120px;
  overflow-y: auto;
}

.failed-batch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 8px;
  margin-bottom: 4px;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  border-left: 3px solid #f56c6c;
}

.batch-info {
  font-size: 12px;
  font-weight: 500;
  color: var(--text-primary);
}

.batch-error {
  font-size: 11px;
  color: #f56c6c;
  max-width: 200px;
  text-align: right;
  word-break: break-word;
}

/* 响应式设计 - 上传进度对话框 */
@media (max-width: 768px) {
  .result-stats {
    grid-template-columns: repeat(2, 1fr);
  }

  .progress-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .failed-batch-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .batch-error {
    max-width: 100%;
    text-align: left;
  }
}
</style>
